from sqlalchemy import <PERSON>umn, Inte<PERSON>, String, BigI<PERSON>ger, Enum
from . import Base, db_session
import time


class NodeStatus(Base):
    __tablename__ = 'node_status'

    id = Column(Integer, primary_key=True, autoincrement=True)
    node_id = Column(String(255), nullable=False, unique=True)
    group_id = Column(String(255), nullable=False)
    endpoint = Column(String(255), nullable=False)
    role = Column(Enum('leader', 'follower', 'candidate'), nullable=False, default='follower')
    status = Column(Enum('running', 'error'), nullable=False, default='running')
    last_applied = Column(Integer, nullable=False, default=0)
    last_heartbeat = Column(BigInteger, nullable=False, default=lambda: int(time.time() * 1000))


def create_node_status(node_status: NodeStatus):
    with db_session() as session:
        session.add(node_status)
        session.flush()
        session.refresh(node_status)
        session.expunge(node_status)
        return node_status


def get_node_status(node_status_id: int):
    with db_session() as session:
        node_status = session.query(NodeStatus).filter(NodeStatus.id == node_status_id).first()
        if node_status:
            session.expunge(node_status)
        return node_status


def get_node_statuses(skip: int = 0, limit: int = 100):
    with db_session() as session:
        node_statuses = session.query(NodeStatus).offset(skip).limit(limit).all()
        for status in node_statuses:
            session.expunge(status)
        return node_statuses


def update_node_status(node_status_id: int, node_status_data: dict):
    with db_session() as session:
        session.query(NodeStatus).filter(NodeStatus.id == node_status_id).update(node_status_data)
        updated_status = session.query(NodeStatus).filter(NodeStatus.id == node_status_id).first()
        if updated_status:
            session.expunge(updated_status)
        return updated_status


def delete_node_status(node_status_id: int):
    with db_session() as session:
        node_status = session.query(NodeStatus).filter(NodeStatus.id == node_status_id).first()
        if node_status:
            session.delete(node_status)
            session.flush()
            session.expunge(node_status)
        return node_status


def get_node_status_by_node_id(node_id: str):
    """Get node status by node_id"""
    with db_session() as session:
        node_status = session.query(NodeStatus).filter(NodeStatus.node_id == node_id).first()
        if node_status:
            session.expunge(node_status)
        return node_status


def get_nodes_by_group_id(group_id: str):
    """Get all nodes in a specific group"""
    with db_session() as session:
        nodes = session.query(NodeStatus).filter(NodeStatus.group_id == group_id).all()
        for node in nodes:
            session.expunge(node)
        return nodes


def update_node_heartbeat(node_id: str, status: str = 'running'):
    """Update node heartbeat timestamp and status"""
    with db_session() as session:
        current_time = int(time.time() * 1000)
        session.query(NodeStatus).filter(NodeStatus.node_id == node_id).update({
            'last_heartbeat': current_time,
            'status': status
        })
        updated_node = session.query(NodeStatus).filter(NodeStatus.node_id == node_id).first()
        if updated_node:
            session.expunge(updated_node)
        return updated_node


def update_node_last_applied(node_id: str, last_applied_id: int):
    """Update node's last_applied field"""
    with db_session() as session:
        session.query(NodeStatus).filter(NodeStatus.node_id == node_id).update({
            'last_applied': last_applied_id
        })
        updated_node = session.query(NodeStatus).filter(NodeStatus.node_id == node_id).first()
        if updated_node:
            session.expunge(updated_node)
        return updated_node


def register_or_update_node(node_id: str, group_id: str, endpoint: str):
    """Register a new node or update existing node information"""
    with db_session() as session:
        existing_node = session.query(NodeStatus).filter(NodeStatus.node_id == node_id).first()
        current_time = int(time.time() * 1000)

        if existing_node:
            # Update existing node
            session.query(NodeStatus).filter(NodeStatus.node_id == node_id).update({
                'group_id': group_id,
                'endpoint': endpoint,
                'status': 'running',
                'last_heartbeat': current_time
            })
            updated_node = session.query(NodeStatus).filter(NodeStatus.node_id == node_id).first()
            if updated_node:
                session.expunge(updated_node)
            return updated_node
        else:
            # Create new node
            new_node = NodeStatus(
                node_id=node_id,
                group_id=group_id,
                endpoint=endpoint,
                role='follower',
                status='running',
                last_applied=0,
                last_heartbeat=current_time
            )
            session.add(new_node)
            session.flush()
            session.refresh(new_node)
            session.expunge(new_node)
            return new_node
