from sqlalchemy import <PERSON>umn, Integer, String, BigInteger, JSON, Enum
from . import Base, db_session
import time


class RedoLog(Base):
    __tablename__ = 'redo_log'

    id = Column(Integer, primary_key=True, autoincrement=True)
    type = Column(Enum('create', 'update', 'delete'), nullable=False)
    object = Column(Enum('network', 'eip_binding', 'eip_snat', 'eip_dnat'), nullable=False)
    data = Column(JSON, nullable=False)
    time = Column(BigInteger, nullable=False, default=lambda: int(time.time() * 1000))


def create_redo_log(redo_log: RedoLog):
    with db_session() as session:
        session.add(redo_log)
        session.flush()
        session.refresh(redo_log)
        session.expunge(redo_log)
        return redo_log


def get_redo_log(redo_log_id: int):
    with db_session() as session:
        redo_log = session.query(RedoLog).filter(RedoLog.id == redo_log_id).first()
        if redo_log:
            session.expunge(redo_log)
        return redo_log


def get_redo_logs(skip: int = 0, limit: int = 100):
    with db_session() as session:
        redo_logs = session.query(RedoLog).offset(skip).limit(limit).all()
        for log in redo_logs:
            session.expunge(log)
        return redo_logs


def get_redo_logs_after_id(last_applied_id: int, limit: int = 100):
    """Get redo logs with ID greater than last_applied_id"""
    with db_session() as session:
        redo_logs = session.query(RedoLog).filter(RedoLog.id > last_applied_id).order_by(RedoLog.id).limit(limit).all()
        for log in redo_logs:
            session.expunge(log)
        return redo_logs


def get_latest_redo_log_id():
    """Get the ID of the latest redo log entry"""
    with db_session() as session:
        latest_log = session.query(RedoLog).order_by(RedoLog.id.desc()).first()
        return latest_log.id if latest_log else 0
