"""
Database service for core_service operations.
Provides CRUD operations for subnet, EIP mount, SNAT, and DNAT data using MySQL database.
"""

import logging
import json
from typing import Dict, Any, List, Optional
from v_switch.db.network import Network, create_network, get_network, update_network, delete_network, get_networks
from v_switch.db.eip_binding import EipBinding, create_eip_binding, get_eip_binding, update_eip_binding, delete_eip_binding
from v_switch.db.eip_snat import EipSnat, create_eip_snat, get_eip_snat, update_eip_snat, delete_eip_snat
from v_switch.db.eip_dnat import EipDnat, create_eip_dnat, get_eip_dnat, update_eip_dnat, delete_eip_dnat
from v_switch.db.redo_log import RedoLog, create_redo_log, get_redo_logs_after_id, get_latest_redo_log_id
from v_switch.db.node_status import get_nodes_by_group_id


class DatabaseService:
    """Database service for managing network configurations in MySQL."""

    def __init__(self):
        """Initialize database service."""
        self.logger = logging.getLogger(__name__)

    def create_subnet_record(self, tenant_id: str, vlan_id: int, subnet_data: Dict[str, Any]) -> Optional[int]:
        """Create a subnet record in the database.
        
        Args:
            tenant_id: Tenant identifier
            vlan_id: VLAN identifier
            subnet_data: Subnet configuration data
            
        Returns:
            Network ID if successful, None otherwise
        """
        try:
            network = Network(
                server_type=subnet_data.get('server_type', 'guest'),
                tenant_id=tenant_id,
                vlan_id=vlan_id,
                ip_start=subnet_data.get('ip_start', ''),
                ip_end=subnet_data.get('ip_end', ''),
                ip_mask=str(subnet_data.get('ip_mask', 24)),
                ip_gateway=subnet_data.get('ip_gateway', ''),
                ip6_start=subnet_data.get('ip6_start'),
                ip6_end=subnet_data.get('ip6_end'),
                ip6_mask=subnet_data.get('ip6_mask'),
                ip6_gateway=subnet_data.get('ip6_gateway'),
                monitor_ip=subnet_data.get('monitor_ip'),
                monitor_gateway_ip=subnet_data.get('monitor_gateway_ip')
            )
            
            created_network = create_network(network)
            if created_network:
                self.logger.info(f"Created subnet record for tenant {tenant_id}, VLAN {vlan_id}")
                return created_network.id
            return None
            
        except Exception as e:
            self.logger.error(f"Error creating subnet record: {e}")
            return None

    def get_subnet_record(self, tenant_id: str, vlan_id: int) -> Optional[Network]:
        """Get subnet record from database.
        
        Args:
            tenant_id: Tenant identifier
            vlan_id: VLAN identifier
            
        Returns:
            Network record if found, None otherwise
        """
        try:
            # For now, we'll search by vlan_id and tenant_id
            # This is a simplified approach - in production you might want to add an index
            networks = get_networks(skip=0, limit=1000)  # Get a reasonable batch
            for network in networks:
                if network.tenant_id == tenant_id and network.vlan_id == vlan_id:
                    return network
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting subnet record: {e}")
            return None

    def update_subnet_record(self, network_id: int, subnet_data: Dict[str, Any]) -> bool:
        """Update subnet record in database.
        
        Args:
            network_id: Network ID
            subnet_data: Updated subnet data
            
        Returns:
            True if successful
        """
        try:
            update_data = {}
            if 'ip_start' in subnet_data:
                update_data['ip_start'] = subnet_data['ip_start']
            if 'ip_end' in subnet_data:
                update_data['ip_end'] = subnet_data['ip_end']
            if 'ip_gateway' in subnet_data:
                update_data['ip_gateway'] = subnet_data['ip_gateway']
            if 'monitor_ip' in subnet_data:
                update_data['monitor_ip'] = subnet_data['monitor_ip']
            if 'monitor_gateway_ip' in subnet_data:
                update_data['monitor_gateway_ip'] = subnet_data['monitor_gateway_ip']
                
            updated_network = update_network(network_id, update_data)
            return updated_network is not None
            
        except Exception as e:
            self.logger.error(f"Error updating subnet record: {e}")
            return False

    def delete_subnet_record(self, network_id: int) -> bool:
        """Delete subnet record from database.
        
        Args:
            network_id: Network ID
            
        Returns:
            True if successful
        """
        try:
            deleted_network = delete_network(network_id)
            return deleted_network is not None
            
        except Exception as e:
            self.logger.error(f"Error deleting subnet record: {e}")
            return False

    def create_eip_mount_record(self, subnet_id: int, eip: str, internal_ip: str, 
                               eip_vlan_id: int, rate: Optional[int] = None, 
                               ceil: Optional[int] = None) -> Optional[int]:
        """Create EIP mount record in database.
        
        Args:
            subnet_id: Subnet ID
            eip: External IP address
            internal_ip: Internal IP address
            eip_vlan_id: EIP VLAN ID
            rate: Rate limit
            ceil: Ceiling limit
            
        Returns:
            EIP binding ID if successful, None otherwise
        """
        try:
            eip_binding = EipBinding(
                eip=eip,
                eip_vlan_id=eip_vlan_id,
                internal_ip=internal_ip,
                sub_vlan_id=subnet_id,  # Using subnet_id as sub_vlan_id
                rate=rate,
                ceil=ceil
            )
            
            created_binding = create_eip_binding(eip_binding)
            if created_binding:
                self.logger.info(f"Created EIP mount record for {eip} -> {internal_ip}")
                return created_binding.id
            return None
            
        except Exception as e:
            self.logger.error(f"Error creating EIP mount record: {e}")
            return None

    def create_snat_record(self, subnet_id: int, eip: str, gateway_ip: str, mask: str) -> Optional[int]:
        """Create SNAT record in database.
        
        Args:
            subnet_id: Subnet ID
            eip: External IP address
            gateway_ip: Gateway IP address
            mask: Network mask
            
        Returns:
            SNAT ID if successful, None otherwise
        """
        try:
            eip_snat = EipSnat(
                subnet_id=subnet_id,
                eip=eip,
                gateway_ip=gateway_ip,
                mask=mask
            )
            
            created_snat = create_eip_snat(eip_snat)
            if created_snat:
                self.logger.info(f"Created SNAT record for subnet {subnet_id}, EIP {eip}")
                return created_snat.id
            return None
            
        except Exception as e:
            self.logger.error(f"Error creating SNAT record: {e}")
            return None

    def create_dnat_record(self, subnet_id: int, eip: str, internal_ip: str) -> Optional[int]:
        """Create DNAT record in database.
        
        Args:
            subnet_id: Subnet ID
            eip: External IP address
            internal_ip: Internal IP address
            
        Returns:
            DNAT ID if successful, None otherwise
        """
        try:
            eip_dnat = EipDnat(
                subnet_id=subnet_id,
                eip=eip,
                internal_ip=internal_ip
            )
            
            created_dnat = create_eip_dnat(eip_dnat)
            if created_dnat:
                self.logger.info(f"Created DNAT record for subnet {subnet_id}, EIP {eip}")
                return created_dnat.id
            return None
            
        except Exception as e:
            self.logger.error(f"Error creating DNAT record: {e}")
            return None

    def log_operation(self, operation_type: str, object_type: str, data: Dict[str, Any]) -> Optional[int]:
        """Log operation to redo_log table.
        
        Args:
            operation_type: Type of operation (create, update, delete)
            object_type: Type of object (network, eip_binding, eip_snat, eip_dnat)
            data: Operation data
            
        Returns:
            Redo log ID if successful, None otherwise
        """
        try:
            redo_log = RedoLog(
                type=operation_type,
                object=object_type,
                data=data
            )
            
            created_log = create_redo_log(redo_log)
            if created_log:
                self.logger.debug(f"Logged operation: {operation_type} {object_type}")
                return created_log.id
            return None
            
        except Exception as e:
            self.logger.error(f"Error logging operation: {e}")
            return None

    def get_nodes_for_group(self, group_id: str) -> List[Any]:
        """Get all nodes for a specific group.
        
        Args:
            group_id: Group identifier
            
        Returns:
            List of node status records
        """
        try:
            return get_nodes_by_group_id(group_id)
        except Exception as e:
            self.logger.error(f"Error getting nodes for group {group_id}: {e}")
            return []
