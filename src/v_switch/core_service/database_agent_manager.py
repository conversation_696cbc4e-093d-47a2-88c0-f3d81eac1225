"""
Database-based agent manager for core_service.
Manages agent selection and communication using MySQL database.
"""

import logging
import requests
from typing import Dict, Any, List, Optional
from v_switch.db.node_status import get_nodes_by_group_id, get_node_status_by_node_id
from v_switch.db.redo_log import get_redo_logs_after_id


class DatabaseAgentManager:
    """Database-based agent manager for selecting and communicating with agents."""

    def __init__(self):
        """Initialize database agent manager."""
        self.logger = logging.getLogger(__name__)

    def get_group_id_for_vlan(self, vlan_id: int, total_groups: int = 4) -> str:
        """Get group ID for a VLAN using modulo operation.
        
        Args:
            vlan_id: VLAN identifier
            total_groups: Total number of groups (default: 4)
            
        Returns:
            Group ID string
        """
        group_id = vlan_id % total_groups
        return f"group-{group_id}"

    def get_agents_for_vlan(self, vlan_id: int) -> List[Any]:
        """Get all agents that should handle a specific VLAN.
        
        Args:
            vlan_id: VLAN identifier
            
        Returns:
            List of agent node status records
        """
        try:
            group_id = self.get_group_id_for_vlan(vlan_id)
            agents = get_nodes_by_group_id(group_id)
            
            # Filter for running agents only
            running_agents = [agent for agent in agents if agent.status == 'running']
            
            self.logger.debug(f"Found {len(running_agents)} running agents for VLAN {vlan_id} in {group_id}")
            return running_agents
            
        except Exception as e:
            self.logger.error(f"Error getting agents for VLAN {vlan_id}: {e}")
            return []

    def call_agent_operation(self, agent_endpoint: str, operation: str, data: Dict[str, Any]) -> bool:
        """Call an agent operation via HTTP API.
        
        Args:
            agent_endpoint: Agent endpoint URL
            operation: Operation name
            data: Operation data
            
        Returns:
            True if successful
        """
        try:
            # Construct the full URL for the agent operation
            url = f"http://{agent_endpoint}/api/operation/{operation}"
            
            # Make HTTP POST request to agent
            response = requests.post(
                url,
                json=data,
                timeout=30,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                self.logger.debug(f"Successfully called {operation} on agent {agent_endpoint}")
                return True
            else:
                self.logger.error(f"Agent {agent_endpoint} returned status {response.status_code} for {operation}")
                return False
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error calling agent {agent_endpoint} for {operation}: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Unexpected error calling agent {agent_endpoint}: {e}")
            return False

    def notify_agents_of_operation(self, vlan_id: int, operation_type: str, 
                                  object_type: str, data: Dict[str, Any], 
                                  redo_log_id: int) -> bool:
        """Notify all relevant agents of an operation.
        
        Args:
            vlan_id: VLAN identifier
            operation_type: Type of operation (create, update, delete)
            object_type: Type of object (network, eip_binding, eip_snat, eip_dnat)
            data: Operation data
            redo_log_id: Redo log ID for this operation
            
        Returns:
            True if at least one agent was successfully notified
        """
        try:
            agents = self.get_agents_for_vlan(vlan_id)
            
            if not agents:
                self.logger.warning(f"No running agents found for VLAN {vlan_id}")
                return False

            # Prepare operation data for agents
            operation_data = {
                'operation_type': operation_type,
                'object_type': object_type,
                'vlan_id': vlan_id,
                'redo_log_id': redo_log_id,
                'data': data
            }

            success_count = 0
            
            # Call all agents in round-robin fashion
            for agent in agents:
                try:
                    if self.call_agent_operation(agent.endpoint, 'execute', operation_data):
                        success_count += 1
                        self.logger.info(f"Successfully notified agent {agent.node_id} of {operation_type} {object_type}")
                    else:
                        self.logger.warning(f"Failed to notify agent {agent.node_id} of {operation_type} {object_type}")
                        
                except Exception as e:
                    self.logger.error(f"Error notifying agent {agent.node_id}: {e}")
                    continue

            if success_count > 0:
                self.logger.info(f"Successfully notified {success_count}/{len(agents)} agents for VLAN {vlan_id}")
                return True
            else:
                self.logger.error(f"Failed to notify any agents for VLAN {vlan_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error notifying agents of operation: {e}")
            return False

    def check_agent_status(self, node_id: str) -> bool:
        """Check if a specific agent is running.
        
        Args:
            node_id: Node identifier
            
        Returns:
            True if agent is running
        """
        try:
            agent = get_node_status_by_node_id(node_id)
            return agent is not None and agent.status == 'running'
            
        except Exception as e:
            self.logger.error(f"Error checking agent status for {node_id}: {e}")
            return False

    def get_all_running_agents(self) -> List[Any]:
        """Get all running agents across all groups.
        
        Returns:
            List of running agent node status records
        """
        try:
            # This is a simplified approach - in production you might want to optimize this
            all_agents = []
            
            # Check multiple groups (assuming we have groups 0-3)
            for group_num in range(4):
                group_id = f"group-{group_num}"
                group_agents = get_nodes_by_group_id(group_id)
                running_agents = [agent for agent in group_agents if agent.status == 'running']
                all_agents.extend(running_agents)
            
            return all_agents
            
        except Exception as e:
            self.logger.error(f"Error getting all running agents: {e}")
            return []

    def get_agent_sync_data(self, node_id: str) -> List[Any]:
        """Get synchronization data for an agent based on its last_applied.
        
        Args:
            node_id: Node identifier
            
        Returns:
            List of redo log entries that the agent needs to process
        """
        try:
            agent = get_node_status_by_node_id(node_id)
            if not agent:
                self.logger.error(f"Agent {node_id} not found")
                return []
            
            # Get redo logs after the agent's last_applied
            redo_logs = get_redo_logs_after_id(agent.last_applied, limit=100)
            
            self.logger.debug(f"Found {len(redo_logs)} redo logs for agent {node_id} sync")
            return redo_logs
            
        except Exception as e:
            self.logger.error(f"Error getting sync data for agent {node_id}: {e}")
            return []

    def ping_agent(self, agent_endpoint: str) -> bool:
        """Ping an agent to check if it's responsive.
        
        Args:
            agent_endpoint: Agent endpoint URL
            
        Returns:
            True if agent is responsive
        """
        try:
            url = f"http://{agent_endpoint}/health"
            response = requests.get(url, timeout=5)
            return response.status_code == 200
            
        except Exception as e:
            self.logger.debug(f"Agent {agent_endpoint} ping failed: {e}")
            return False
