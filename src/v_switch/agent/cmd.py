

import logging
import subprocess
from typing import Tuple
from dataclasses import dataclass, field


@dataclass
class Process:
    args: str
    code: int
    output: str
    error: str

    @classmethod
    def from_result(self, result: subprocess.CompletedProcess):
        return Process(
            args=result.args,
            output=result.stdout.strip(),
            error=result.stderr.strip(),
            code=result.returncode,
        )

    @classmethod
    def from_init(self, args: str, code: int = 0, output: str = "", error: str = ""):
        return Process(
            args=args,
            output=output,
            error=error,
            code=code,
        )

    def is_successful(self) -> bool:
        return self.code == 0


class Grep():

    def __init__(self):
        self._base = "grep"
        self._pattern = None
        self._options = set()

        self._awk = None

    def option(self, option: str):
        """指定 grep 选项, 
        可以多次指定(会自动去重), 
        也可以一次指定多个选项(无法去重)

        例: 
        option("w")  # -w
        option("iw") # -iw
        option("i").option("w")  # -iw
        option("iw").option("w")  # -iww(这种会导致选项出现重复)
        """
        self._options.add(option)
        return self

    def pattern(self, pattern: str):
        """指定 grep 规则, 只能指定一个规则, 多次调用会覆盖之前的规则
        """
        self._pattern = pattern
        return self

    def awk(self, awk: str):
        """指定 awk 命令
        """
        self._awk = awk
        return self

    def has_pattern(self) -> bool:
        """检查是否已经指定了 grep 规则
        """
        return self._pattern is not None

    def build(self):
        """生成 grep 命令
        """
        cmd = ""
        if not self._pattern:
            raise ValueError("pattern is not set")
        if self._options:
            option = ''.join(self._options)
            cmd = f"{self._base} -{option} '{self._pattern}'"
        else:
            cmd = f"{self._base} '{self._pattern}'"
        if self._awk:
            cmd += f" | awk '{self._awk}'"
        return cmd


class CmdExec:

    def __init__(self):
        self._namespace = None
        self.logger = logging.getLogger(__name__)
        self._options = []

    def option(self, option: str):
        """指定 grep 选项, 
        可以多次指定(会自动去重), 
        也可以一次指定多个选项(无法去重)

        例: 
        option("w")  # -w
        option("iw") # -iw
        option("i").option("w")  # -iw
        option("iw").option("w")  # -iww(这种会导致选项出现重复)
        """
        self._options.append(option)
        return self

    def namespace(self, namespace: str):
        """指定命令执行的命名空间
        """
        self._namespace = namespace
        return self

    def test(self, cmd: str) -> Process:
        """测试执行命令, 只打印命令, 不执行
        """
        if self._namespace:
            cmd = f"ip netns exec {self._namespace} {cmd}"

        self.logger.info(f"Command succeeded: {cmd}")
        return Process.from_init(args=cmd)

    def exec(self, cmd: str, greps: list[Grep] = None) -> Process:
        """执行单个命令

        Args:
            task: 任务数据
            cmd: 要执行的命令

        Returns:
            Tuple of (success, output, error)
        """
        try:
            if self._namespace:
                cmd = f"ip netns exec {self._namespace} {cmd}"

            if greps:
                for grep in greps:
                    cmd += f" | {grep.build()}"

            result = subprocess.run(
                cmd,
                shell=True,
                capture_output=True,
                text=True,
                timeout=30
            )
            process = Process.from_result(result)
            if process.is_successful:
                self.logger.info(f"Command succeeded: {cmd} \nOutput: {process.output}")
            else:
                self.logger.error(f"Command failed: {cmd} \nError: {process.error}")

            return process

        except subprocess.TimeoutExpired:
            error_msg = f"Command timeout: [{cmd}]"
            self.logger.error(error_msg)

        except Exception as e:
            error_msg = f"Command [{cmd}] execution error: {e}"
            self.logger.error(error_msg)

        return Process.from_init(args=cmd, code=1, error=error_msg)


class IP(CmdExec):

    def __init__(self):
        super().__init__()
        self._device = None
        self._target = None

    def dev(self, device: str):
        """指定设备名称
        """
        self._device = device
        return self

    def target(self, ip: str):
        """指定 IP 地址
        例:
        127.0.0.1/8
        ***********/24
        """
        self._target = ip
        return self

    def link(self) -> "IpLinkCmd":
        """指定链路命令
        """
        return IpLinkCmd()

    def addr(self) -> "IpAddrCmd":
        """指定地址命令
        """
        return IpAddrCmd()
    
    def netns(self):
        """指定命名空间
        """
        return IpNetnsCmd()

    def route(self) -> "IpRouteCmd":
        """指定路由命令
        """
        return IpRouteCmd()


class IpLinkCmd(IP):

    def __init__(self):
        super().__init__()
        self._base = "ip link"

    def up(self) -> Process:
        """启动设备
        """
        if not self._device:
            raise ValueError("device is not set")
        cmd = f"{self._base} set {self._device} up"
        process = self.exec(cmd)
        return process

    def down(self) -> Process:
        """关闭设备
        """
        if not self._device:
            raise ValueError("device is not set")
        cmd = f"{self._base} set {self._device} down"
        return self.exec(cmd)

    def netns(self) -> Process:
        """移动设备到命名空间
        """
        if not self._device:
            raise ValueError("device is not set")
        if not self._namespace:
            raise ValueError("namespace is not set")
        cmd = f"{self._base} set {self._device} netns {self._namespace}"
        return self.exec(cmd)

    def show(self, *greps: Grep) -> Process:
        """显示设备信息, 可以指定 grep 规则来过滤结果
        """
        if self._device:
            cmd = f"{self._base} show dev {self._device}"
        else:
            cmd = f"{self._base} show"
        return self.exec(cmd, greps)

    def check(self) -> bool:
        """检查设备是否存在, 会根据当前设置的参数自动生成 grep 规则

        Returns:
            True if device exists
        """
        if not self._device:
            raise ValueError("device is not set")
        grep = Grep().option("w").pattern(self._device)
        return self.show(grep).is_successful()


class IpAddrCmd(IP):

    def __init__(self):
        super().__init__()
        self._base = "ip addr"

    def build(self, base_cmd: str):
        """构建命令
        """
        if self._device:
            base_cmd += f" dev {self._device}"
        return base_cmd

    def add(self) -> Process:
        """添加地址
        """
        if not self._target:
            raise ValueError("target is not set")
        if not self._device:
            raise ValueError("device is not set")
        cmd = self.build(f"{self._base} add {self._target}")
        return self.exec(cmd)

    def delete(self) -> Process:
        """删除地址
        """
        if not self._target:
            raise ValueError("target is not set")
        if not self._device:
            raise ValueError("device is not set")
        cmd = self.build(f"{self._base} del {self._target}")
        return self.exec(cmd)

    def show(self, *greps: Grep) -> Process:
        """显示地址信息, 可以指定 grep 规则来过滤结果
        """
        cmd = self.build(f"{self._base} show")
        return self.exec(cmd, greps)

    def check(self) -> bool:
        """检查地址是否存在, 会根据当前设置的参数自动生成 grep 规则

        Returns:
            True if address exists
        """
        if not self._target:
            raise ValueError("target is not set")
        if not self._device:
            raise ValueError("device is not set")
        grep = Grep().option("w").pattern(self._target)
        return self.show(grep).is_successful()


class IpRouteCmd(IP):

    def __init__(self):
        super().__init__()
        self._base = "ip route"
        self._via = None
        self._src = None
        self._metric = None

    def via(self, via: str):
        """指定网关
        """
        self._via = via
        return self

    def src(self, src: str):
        """指定源地址
        """
        self._src = src
        return self

    def metric(self, metric: int):
        """指定路由优先级
        """
        self._metric = metric
        return self

    def build(self, cmd: str):
        if self._via:
            cmd += f" via {self._via}"
        if self._device:
            cmd += f" dev {self._device}"
        if self._src:
            cmd += f" src {self._src}"
        if self._metric:
            cmd += f" metric {self._metric}"
        return cmd

    def add(self) -> Process:
        """添加路由
        """
        if not self._target:
            raise ValueError("target is not set")
        cmd = self.build(f"{self._base} add {self._target}")
        return self.exec(cmd)

    def delete(self) -> Process:
        """删除路由
        """
        if not self._target:
            raise ValueError("target is not set")
        cmd = self.build(f"{self._base} del {self._target}")
        return self.exec(cmd)

    def show(self, *greps: Grep) -> Process:
        """显示路由信息, 可以指定 grep 规则来过滤结果
        """
        cmd = f"{self._base} show"
        return self.exec(cmd, greps)

    def list(self, *greps: Grep) -> Process:
        """列出路由信息, 可以指定 grep 规则来过滤结果
        """
        cmd = f"{self._base} list"
        return self.exec(cmd, greps)

    def check(self) -> bool:
        """检查路由是否存在, 会根据当前设置的参数自动生成 grep 规则

        Returns:
            True if route exists
        """
        rule = self.build(self._target)
        grep = Grep().option("w").pattern(rule)
        process = self.show(grep)
        return process.is_successful()

class IpNetnsCmd(IP):
    def __init__(self):
        super().__init__()
        self._base = "ip netns"
        self._namespace = None

    def add(self) -> Process:
        """添加命名空间
        """
        cmd = f"{self._base} add {self._namespace}"
        if self.check():
            return Process.from_init(args=cmd, output="namespace already exists")
        return self.exec(cmd)
    
    def delete(self) -> Process:
        """删除命名空间
        """
        cmd = f"{self._base} del {self._namespace}"
        if not self.check():
            return Process.from_init(args=cmd, output="namespace not exists")
        return self.exec(cmd)
    
    def check(self) -> bool:
        """检查命名空间是否存在
        """
        if not self._namespace:
            raise ValueError("namespace is not set")
        cmd = f"{self._base} list | grep -w {self._namespace}"
        return self.exec(cmd).is_successful()

class OvsCtl(CmdExec):
    def __init__(self):
        super().__init__()
        self._base = "ovs-vsctl"
        self._bridge = None
        self._port = None
        self._tag = None
        self._trunks = None
        self._type = None

    def bridge(self, bridge: str):
        """指定网桥名称
        """
        self._bridge = bridge
        return self

    def port(self, port: str):
        """指定端口名称
        """
        self._port = port
        return self

    def tag(self, tag: int):
        """指定端口的 VLAN 标签, 和 trunks 互斥
        """
        self._tag = tag
        return self

    def trunks(self, trunks: list[int]):
        """指定端口的 VLAN 干线, 和 tag 互斥
        trunks: list of int
        """
        self._trunks = trunks
        return self

    def type(self, type: str):
        """指定端口的类型, 如 internal, vxlan 等
        """
        self._type = type
        return self

    def type_internal(self):
        """指定端口的类型为 internal
        """
        self._type = "internal"
        return self

    def type_vxlan(self):
        """指定端口的类型为 vxlan
        """
        self._type = "vxlan"
        return self

    def show(self, *greps: Grep) -> Process:
        """显示 OVS 数据的简要概览，包括网桥、端口和管理器的信息
        """
        cmd = f"{self._base} show"
        return self.exec(cmd, greps)

    def add_bridge(self) -> Process:
        """添加网桥
        """
        cmd = f"{self._base} add-br {self._bridge}"
        return self.exec(cmd)

    def del_bridge(self) -> Process:
        """删除网桥
        """
        cmd = f"{self._base} del-br {self._bridge}"
        return self.exec(cmd)

    def build_interface(self):
        """构建端口设置命令
        """
        cmd = f"set interface {self._port}"
        if self._type:
            cmd += f" type={self._type}"
        if self._trunks:
            cmd += f" trunks={self._trunks}"
        elif self._tag:
            cmd += f" tag={self._tag}"
        return cmd

    def add_port(self, join_set: bool) -> Process:
        """添加端口, 可以指定是否将端口设置和端口添加合并为一个命令
        """
        cmd = f"{self._base} add-port"
        if self._bridge:
            cmd += f" {self._bridge}"
        else:
            raise ValueError("bridge is not set")
        if self._port:
            cmd += f" {self._port}"
        else:
            raise ValueError("port is not set")
        if join_set:
            cmd += f" -- {self.build_interface()}"
        return self.exec(cmd)

    def del_port(self) -> Process:
        """删除端口
        """
        cmd = f"{self._base} del-port"
        if self._bridge:
            cmd += f" {self._bridge}"
        else:
            raise ValueError("bridge is not set")
        if self._port:
            cmd += f" {self._port}"
        else:
            raise ValueError("port is not set")
        return self.exec(cmd)

    def set_port(self) -> Process:
        """修改端口设置
        """
        cmd = f"{self._base} set port {self._port}"
        if self._trunks:
            cmd += f" trunks={self._trunks}"
        elif self._tag:
            cmd += f" tag={self._tag}"
        else:
            raise ValueError("tag and trunks are not set")
        return self.exec(cmd)

    def clear_port(self) -> Process:
        """清除端口设置
        """
        if not self._port:
            raise ValueError("port is not set")
        cmd = f"{self._base} clear port {self._port}"
        if self._trunks:
            cmd += " trunks"
        elif self._tag:
            cmd += " tag"
        else:
            raise ValueError("tag and trunks are not set")
        return self.exec(cmd)

    def list_ports(self, *greps: Grep) -> Process:
        """列出网桥上的所有端口, 可以指定 grep 规则来过滤结果
        """
        cmd = f"{self._base} list-ports {self._bridge}"
        return self.exec(cmd, greps)

    def check_port(self) -> bool:
        """检查端口是否存在, 会根据当前设置的参数自动生成 grep 规则

        Returns:
            True if port exists
        """
        grep = Grep().option("w").pattern(self._port)
        return self.list_ports(grep).is_successful()


class NftTable():
    def __init__(self):
        self._family = "ip"
        self._name = None

    def family(self, family: str):
        """指定协议族 
            ip: 仅处理 IPv4 数据包（默认）。
            ip6: 仅处理 IPv6 数据包。
            inet: 同时适用于 IPv4 和 IPv6 数据包，可以简化双栈管理。
            arp: 处理 ARP 数据包。
            bridge: 处理桥接设备上的数据包。
            netdev: 用于入口流量过滤。
        """
        if family not in ["ip", "ip6", "inet", "arp", "bridge", "netdev"]:
            raise ValueError(f"invalid family: {family}")
        self._family = family
        return self

    def ip(self):
        """指定协议族为 ip
        """
        self._family = "ip"
        return self

    def ip6(self):
        """指定协议族为 ip6
        """
        self._family = "ip6"
        return self

    def inet(self):
        """指定协议族为 inet
        """
        self._family = "inet"
        return self

    def arp(self):
        """指定协议族为 arp
        """
        self._family = "arp"
        return self

    def bridge(self):
        """指定协议族为 bridge
        """
        self._family = "bridge"
        return self

    def netdev(self):
        """指定协议族为 netdev
        """
        self._family = "netdev"
        return self

    def name(self, name: str):
        """指定表名称
        """
        self._name = name
        return self

    def build(self):
        """生成表名称
        """
        if not self._name:
            raise ValueError("name is not set")
        return f"{self._family} {self._name}"


class NftChain():
    def __init__(self):
        self._name = None
        self._type = None
        self._hook = None
        self._priority = None
        self._handle = None

    def name(self, name: str):
        """指定链名称
        """
        self._name = name
        return self

    def type(self, type: str):
        """指定链类型
        """
        if type not in ["filter", "route", "nat"]:
            raise ValueError(f"invalid chain type: {type}")
        self._type = type
        return self

    def filter(self):
        """指定链类型为 filter
        """
        self._type = "filter"
        return self

    def route(self):
        """指定链类型为 route
        """
        self._type = "route"
        return self

    def nat(self):
        """指定链类型为 nat
        """
        self._type = "nat"
        return self

    def hook(self, hook: str):
        """指定链钩子 (prerouting, input, forward, output, postrouting)
        """
        if hook not in ["prerouting", "input", "forward", "output", "postrouting"]:
            raise ValueError(f"invalid chain hook: {hook}")
        self._hook = hook
        return self

    def prerouting(self):
        """指定链钩子为 prerouting
        """
        self._hook = "prerouting"
        return self

    def input(self):
        """指定链钩子为 input
        """
        self._hook = "input"
        return self

    def forward(self):
        """指定链钩子为 forward
        """
        self._hook = "forward"
        return self

    def output(self):
        """指定链钩子为 output
        """
        self._hook = "output"
        return self

    def postrouting(self):
        """指定链钩子为 postrouting
        """
        self._hook = "postrouting"
        return self

    def priority(self, priority: int):
        """指定链优先级
        """
        self._priority = priority
        return self

    def is_base_chain(self):
        """是否为基础链
        """
        return self._type is not None

    def build(self):
        """生成链名称
        """
        if not self._name:
            raise ValueError("name is not set")
        return self._name

    def build_spec(self):
        """生成链规格
        """
        if not self._name:
            raise ValueError("name is not set")
        if not self._type:
            return ""
        if not self._hook or not self._priority:
            raise ValueError("type, hook and priority must be set")
        return f" {{ type {self._type} hook {self._hook} priority {self._priority} \\; }}"


class NftSet():
    def __init__(self):
        self._name = None
        self._type = None
        self._flags = None
        self._elements = set()

    def name(self, name: str):
        """指定集合名称
        """
        self._name = name
        return self

    def type(self, type: str):
        """指定集合类型
        """
        if type not in ["ipv4_addr", "ipv6_addr", "ether_addr", "inet_service", "mark", "real", "comment"]:
            raise ValueError(f"invalid set type: {type}")
        self._type = type
        return self

    def ipv4_addr(self):
        """指定集合类型为 ipv4_addr
        """
        self._type = "ipv4_addr"
        return self

    def ipv6_addr(self):
        """指定集合类型为 ipv6_addr
        """
        self._type = "ipv6_addr"
        return self

    def ether_addr(self):
        """指定集合类型为 ether_addr
        """
        self._type = "ether_addr"
        return self

    def inet_service(self):
        """指定集合类型为 inet_service
        """
        self._type = "inet_service"
        return self

    def flags(self, *flags: str):
        """指定集合标志
        """
        for flag in flags:
            if flag not in ["interval"]:
                raise ValueError(f"invalid set flag: {flag}")
            self._flags.add(flag)
        return self

    def element(self, *element: str):
        """指定集合元素
        """
        for e in element:
            self._elements.add(e)
        return self

    def build(self):
        """生成集合名称
        """
        if not self._name:
            raise ValueError("name is not set")

        if self._flags:
            flags = "flags " + ','.join(self._flags) + " \\; "
        else:
            flags = ""
        return f"{self._name} {{ type {self._type} \\; {flags}  }}"


class NftMatch():
    def __init__(self, *args: str):
        self._op = ""
        self._protocol = ""
        self._field = None
        if args:
            self._value = set(args)
        else:
            self._value = set()

    def protocol(self, protocol: str):
        """指定协议
        """
        if protocol not in ["ip", "tcp", "udp", "icmp"]:
            raise ValueError(f"invalid protocol: {protocol}")
        self._protocol = protocol
        return self

    def ip(self):
        """指定协议为 ip
        """
        self._protocol = "ip"
        return self

    def tcp(self):
        """指定协议为 tcp
        """
        self._protocol = "tcp"
        return self

    def udp(self):
        """指定协议为 udp
        """
        self._protocol = "udp"
        return self

    def icmp(self):
        """指定协议为 icmp
        """
        self._protocol = "icmp"
        return self

    def field(self, field: str):
        """指定字段
        """
        self._field = field
        return self

    def op(self, op: str):
        """指定操作符
        """
        if op not in ["==", "!=", ">", "<", ">=", "<="]:
            raise ValueError(f"invalid operator: {op}")
        self._op = op
        return self

    def eq(self, value: str):
        """指定值, 等于
        """
        self._op = "=="
        self._value = value
        return self

    def ne(self, value: str):
        """指定值, 不等于
        """
        self._op = "!="
        self._value = value
        return self

    def value(self, *value: str):
        """指定值
        """
        for v in value:
            self._value.add(v)
        return self

    def build(self):
        """生成匹配规则
        """
        if not self._field or not self._value:
            raise ValueError("field and value must be set")

        cmd = f"{self._protocol} {self._field}"
        if self._op and self._op != "==":
            cmd += f" {self._op} "
        else:
            cmd += " "
        if len(self._value) > 1:
            cmd += "{ " + ", ".join(self._value) + " }"
        else:
            cmd += " ".join(self._value)
        return cmd


class NftRule():
    def __init__(self):
        self._saddr: NftMatch = None
        self._daddr: NftMatch = None
        self._sport: NftMatch = None
        self._dport: NftMatch = None
        self._protocol: NftMatch = None
        self._iifname: NftMatch = None
        self._oifname: NftMatch = None
        self._counter: bool = False
        self._verdict: str = None
        self._comment: str = None

    def saddr(self, saddr: NftMatch):
        """指定源地址
        """
        self._saddr = saddr
        self._saddr.ip().field("saddr")
        return self

    def daddr(self, daddr: NftMatch):
        """指定目标地址
        """
        self._daddr = daddr
        self._daddr.ip().field("daddr")
        return self

    def sport(self, sport: NftMatch):
        """指定源端口
        """
        self._sport = sport
        self._sport.tcp().field("sport")
        return self

    def dport(self, dport: NftMatch):
        """指定目标端口
        """
        self._dport = dport
        self._dport.tcp().field("dport")
        return self

    def protocol(self, protocol: NftMatch):
        """指定协议
        """
        self._protocol = protocol
        return self

    def iifname(self, iifname: NftMatch):
        """指定输入接口
        """
        self._iifname = iifname
        self._iifname.field("iifname")
        return self

    def oifname(self, oifname: NftMatch):
        """指定输出接口
        """
        self._oifname = oifname
        self._oifname.field("oifname")
        return self

    def counter(self):
        """指定是否计数
        """
        self._counter = True
        return self

    def comment(self, comment: str):
        """指定注释
        """
        self._comment = comment
        return self

    # 裁决动作 (Verdict Statements)
    def accept(self):
        """ 接受数据包，并允许其继续在网络协议栈中传输。
        """
        self._verdict = "accept"
        return self

    def drop(self):
        """ 静默丢弃数据包，不向发送方返回任何通知。
        """
        self._verdict = "drop"
        return self

    def reject(self, with_type: str):
        """ 拒绝数据包，并向发送方返回一个错误通知。

        Args:
            with_type: 可以通过 with 关键字指定具体的拒绝类型。
              TCP 协议: 
                reject with tcp reset (发送 TCP RST 包)。
              ICMP/ICMPv6 协议:[4]
                reject with icmp type net-unreachable
                reject with icmp type host-unreachable
                reject with icmp type port-unreachable (默认值)
                reject with icmp type net-prohibited
                reject with icmp type host-prohibited
                reject with icmpv6 type no-route
                reject with icmpv6 type addr-unreachable
                reject with icmpv6 type port-unreachable
                reject with icmpv6 type admin-prohibited
        """
        self._verdict = "reject"
        if with_type:
            self._verdict += f" with {with_type}"

    def queue(self, num: str, bypass: bool = False, fanout: bool = False):
        """将数据包传递给用户空间队列进行处理。这通常与 libnetfilter_queue 库结合使用。

        Args:
            num <queue_number>: 指定发送到的队列编号（0-65535）。
            num <from>-<to>: 指定一个队列范围用于负载均衡。
            bypass: 如果没有用户空间程序监听该队列，数据包将被视为 accept 而不是被丢弃。
            fanout: 与队列范围一起使用，根据流哈希将数据包分发到指定范围的队列中。
        """
        self._verdict = "queue"
        if num:
            self._verdict += f" num {num}"
        if bypass:
            self._verdict += " bypass"
        if fanout:
            self._verdict += " fanout"

    def jump(self, chain: NftChain):
        """ 跳转到指定的链。
        当该链中的所有规则处理完毕（或者遇到 return 动作）后，控制权会返回到当前链的下一条规则继续执行。
        """
        self._verdict = f"jump {chain._name}"
        return self

    def goto(self, chain: NftChain):
        """ 跳转到指定的链。
        处理完目标链后，不会返回到原始链，而是从目标链继续后续的处理（例如，应用基础链的策略）。
        """
        self._verdict = f"goto {chain._name}"
        return self

    def return_to_caller(self):
        """ 立即停止当前链中的规则评估，并将控制权返回到调用它的上一级链（通常是 jump 动作之后）。
        如果当前是基础链，则 return 的效果等同于应用该链的默认策略（如 accept 或 drop）。
        """
        self._verdict = "return"
        return self

    # NAT (网络地址转换) 动作
    def snat_to(self, target_addr: str):
        """执行 SNAT 规则
        """
        self._verdict = f"snat to {target_addr}"
        return self

    def dnat_to(self, target_addr: str):
        """执行 DNAT 规则
        """
        self._verdict = f"dnat to {target_addr}"
        return self

    def build(self):
        """生成匹配规则
        """
        match = ""
        if self._saddr:
            match += f"{self._saddr.build()} "
        if self._daddr:
            match += f"{self._daddr.build()} "
        if self._sport:
            match += f"{self._sport.build()} "
        if self._dport:
            match += f"{self._dport.build()} "
        if self._protocol:
            match += f"{self._protocol.build()} "
        if self._iifname:
            match += f"{self._iifname.build()} "
        if self._oifname:
            match += f"{self._oifname.build()} "
        if self._counter:
            match += "counter "
        if self._verdict:
            match += f"{self._verdict}"
        return match

    def gen_comment(self):
        """生成注释
           如果手动指定了注释, 则使用手动指定的注释
           如果没有手动指定注释, 则自动生成注释, 格式

        """
        if self._comment:
            return f" comment \"{self._comment}\""
        comment = ""
        if self._saddr:
            comment += f"_saddr_{self._saddr._value}"
        if self._daddr:
            comment += f"_daddr_{self._daddr._value}"
        if self._sport:
            comment += f"_sport_{self._sport._value}"
        if self._dport:
            comment += f"_dport_{self._dport._value}"
        if self._protocol:
            comment += f"_protocol_{self._protocol._value}"
        if self._iifname:
            comment += f"_iifname_{self._iifname._value}"
        if self._oifname:
            comment += f"_oifname_{self._oifname._value}"
        return f" comment \"{comment}\""


class Nft(CmdExec):
    """nftables 命令控制类"""

    def __init__(self):
        super().__init__()
        self._base = "nft"

    # 表操作方法
    def add_table(self, table: NftTable) -> Process:
        """添加表
        """
        if self.check_table(table):
            return Process.from_init("",0,"table already exists","")
        cmd = f"{self._base} add table {table.build()}"
        return self.exec(cmd)

    def del_table(self, table: NftTable) -> Process:
        """删除表
        """
        if not self.check_table(table):
            return Process.from_init("", 0, "table not exists", "")
        cmd = f"{self._base} delete table {table.build()}"
        return self.exec(cmd)

    def list_tables(self, table: NftTable, *greps: Grep) -> Process:
        """列出所有表
        """
        cmd = f"{self._base} list tables {table._family}"
        return self.exec(cmd, greps)

    def list_table(self, table: NftTable, *greps: Grep) -> Process:
        """列出指定表的内容
        """
        cmd = f"{self._base} list table {table.build()}"
        return self.exec(cmd, greps)

    def flush_table(self, table: NftTable) -> Process:
        """清空表中的所有规则
        """
        cmd = f"{self._base} flush table {table.build()}"
        return self.exec(cmd)

    def check_table(self, table: NftTable) -> bool:
        """检查表是否存在

        Returns:
            True if table exists
        """
        grep = Grep().option("w").pattern(f"table {table.build()}")
        return self.list_tables(grep).is_successful()

    # 链操作方法
    def add_chain(self, table: NftTable, chain: NftChain) -> Process:
        """添加链
        """
        if self.check_chain(table, chain):
            return Process.from_init("", 0, "chain already exists", "")
        cmd = f"{self._base} add chain {table.build()} {chain.build()} {chain.build_spec()}"
        return self.exec(cmd)

    def del_chain(self, table: NftTable, chain: NftChain) -> Process:
        """删除链
        """
        if not self.check_chain(table, chain):
            return Process.from_init("", 0, "chain not exists", "")
        cmd = f"{self._base} delete chain {table.build()} {chain.build()}"
        return self.exec(cmd)

    def list_chains(self, table: NftTable, *greps: Grep) -> Process:
        """列出所有链
        """
        cmd = self._base
        if self._options:
            cmd += f" -{''.join(self._options)}"
        cmd += f" list chains"
        if table._family:
            cmd += f" {table._family}"
        return self.exec(cmd, greps)

    def list_chain(self, table: NftTable, chain: NftChain, *greps: Grep) -> Process:
        """列出指定链的内容
        """
        cmd = self._base
        if self._options:
            cmd += f" -{''.join(self._options)}"
        cmd += f" list chain {table.build()} {chain.build()}"
        return self.exec(cmd, greps)

    def flush_chain(self, table: NftTable, chain: NftChain) -> Process:
        """清空链中的所有规则
        """
        cmd = f"{self._base} flush chain {table.build()} {chain.build()}"
        return self.exec(cmd)

    def check_chain(self, table: NftTable, chain: NftChain) -> bool:
        """检查链是否存在

        Returns:
            True if chain exists
        """
        return self.list_chain(table, chain).is_successful()

    # 集合操作方法
    def add_set(self, table: NftTable, set: NftSet) -> Process:
        """添加集合
        """
        if self.check_set(table, set):
            return Process.from_init("",0,"set already exists","")
        cmd = f"{self._base} add set {table.build()} {set.build()}"
        return self.exec(cmd)

    def del_set(self, table: NftTable, set: NftSet) -> Process:
        """删除集合
        """
        cmd = f"{self._base} delete set {table.build()} {set._name}"
        return self.exec(cmd)

    def list_sets(self, table: NftTable = None, *greps: Grep) -> Process:
        """列出所有集合
        """
        cmd = f"{self._base} list sets"
        if table:
            cmd += f" table {table.build()}"
        return self.exec(cmd, greps)

    def get_set(self, table: NftTable, set: NftSet) -> Process:
        """获取集合内容
        """
        cmd = f"{self._base} list set {table.build()} {set._name}"
        return self.exec(cmd)

    def check_set(self, table: NftTable, set: NftSet) -> bool:
        """检查集合是否存在

        Returns:
            True if set exists
        """
        grep = Grep().option("w").pattern(f"set {set._name}")
        return self.list_sets(table, set, grep).is_successful()

    def add_set_element(self, table: NftTable, set: NftSet, *element: str) -> Process:
        """添加集合元素
        """
        cmd = f"{self._base} add element {table.build()} {set.build()} {{ {', '.join(element)} }}"
        return self.exec(cmd)

    def del_set_element(self, table: NftTable, set: NftSet, *element: str) -> Process:
        """删除集合元素
        """
        cmd = f"{self._base} delete element {table.build()} {set.build()} {{ {', '.join(element)} }}"
        return self.exec(cmd)

    def flush_set(self, table: NftTable, set: NftSet) -> Process:
        """清空集合元素
        """
        cmd = f"{self._base} flush set {table.build()} {set._name}"
        return self.exec(cmd)

    # 规则集操作方法
    def list_ruleset(self, *greps: Grep) -> Process:
        """列出所有规则集
        """
        cmd = f"{self._base} list ruleset"
        return self.exec(cmd, greps)

    def flush_ruleset(self) -> Process:
        """清空所有规则集
        """
        cmd = f"{self._base} flush ruleset"
        return self.exec(cmd)

    def _base_add_rule(self, table: NftTable, chain: NftChain):
        """生成基础规则
        """
        return f"{self._base} add rule {table.build()} {chain.build()}"

    def _base_del_rule(self, table: NftTable, chain: NftChain):
        """生成基础规则
        """
        return f"{self._base} delete rule {table.build()} {chain.build()}"

    def check_rule(self, table: NftTable, chain: NftChain, rule: NftRule) -> bool:
        """检查规则是否存在

        Returns:
            True if rule exists
        """
        grep = Grep().option("w").pattern(rule.build())
        return self.list_chain(table, chain, grep).is_successful()

    def add_rule(self, table: NftTable, chain: NftChain, rule: NftRule) -> Process:
        """添加规则
        """
        if self.check_rule(table, chain, rule):
            return Process.from_init("",0, "rule already exists", "")
        cmd = f"{self._base_add_rule(table, chain)} {rule.build()}"
        return self.exec(cmd)

    def del_rule(self, table: NftTable, chain: NftChain, rule: NftRule) -> Process:
        """删除规则
        """
        grep = Grep().option("w").pattern(rule.build()).awk('{print $NF}')
        self.option("as")
        res = self.list_chain(table, chain, grep)
        self._options = None
        if not res.is_successful():
            return Process.from_init("", 0, "rule not found", "")
        cmd = f"{self._base_del_rule(table, chain)} handle {res.output}"
        return self.exec(cmd)

    def del_rule_by_handle(self, handle: str) -> Process:
        """删除规则
        """
        if not handle:
            raise ValueError("handle is not set")
        cmd = self._base_del_rule() + f" handle {handle}"
        return self.exec(cmd)

    def del_rule_by_comment(self, comment: str) -> Process:
        """通过注释删除规则
        """
        cmd = self._base_del_rule() + f" comment \"{comment}\""
        return self.exec(cmd)


class TrafficControl(CmdExec):
    """tc 命令控制类"""

    def __init__(self):
        super().__init__()
        self._base = "tc"
        self._device = None
        self._handle = None
        self._parent = None
        self._type = None
        self._protocol = None
        self._prio = None
        self._match = None
        self._flowid = None
        self._rate = None
        self._ceil = None
        self._options = None

    def device(self, device: str):
        """指定设备名称
        """
        self._device = device
        return self

    def handle(self, handle: str):
        """指定句柄
        """
        self._handle = handle
        return self

    def parent(self, parent: str):
        """指定父节点
        """
        self._parent = parent
        return self

    def root(self):
        """指定根节点
        """
        self._parent = "root"
        return self

    def ingress(self):
        """指定入口流量控制
        """
        self._parent = "ingress"
        self._handle = "ffff:"
        return self

    def type(self, qdisc_type: str):
        """指定队列规则类型
        """
        self._type = qdisc_type
        return self

    def htb(self):
        """指定HTB队列规则
        """
        self._type = "htb"
        return self

    def pfifo(self):
        """指定PFIFO队列规则
        """
        self._type = "pfifo"
        return self

    def sfq(self):
        """指定SFQ队列规则
        """
        self._type = "sfq"
        return self

    def fq_codel(self):
        """指定FQ_CODEL队列规则
        """
        self._type = "fq_codel"
        return self

    def protocol(self, protocol: str):
        """指定协议
        """
        self._protocol = protocol
        return self

    def ip_protocol(self):
        """指定IP协议
        """
        self._protocol = "ip"
        return self

    def prio(self, prio: int):
        """指定优先级
        """
        self._prio = prio
        return self

    def match(self, match: str):
        """指定匹配条件
        """
        self._match = match
        return self

    def flowid(self, flowid: str):
        """指定流量流向
        """
        self._flowid = flowid
        return self

    def rate(self, rate: str):
        """指定速率
        """
        self._rate = rate
        return self

    def ceil(self, ceil: str):
        """指定上限速率
        """
        self._ceil = ceil
        return self

    def options(self, options: str):
        """指定选项
        """
        self._options = options
        return self

    def qdisc(self) -> "TcQdisc":
        """队列规则操作
        """
        return TcQdisc().device(self._device).namespace(self._namespace)

    def class_cmd(self) -> "TcClass":
        """类操作
        """
        return TcClass().device(self._device).namespace(self._namespace)

    def filter_cmd(self) -> "TcFilter":
        """过滤器操作
        """
        return TcFilter().device(self._device).namespace(self._namespace)

    def clear(self) -> Process:
        """清理所有tc规则
        """
        if not self._device:
            raise ValueError("device is not set")

        # 清理root队列规则
        cmd1 = f"{self._base} qdisc del dev {self._device} root || true"
        process1 = self.exec(cmd1)

        # 清理ingress队列规则
        cmd2 = f"{self._base} qdisc del dev {self._device} ingress || true"
        process2 = self.exec(cmd2)

        # 返回最后一个命令的结果
        return process2


class TcQdisc(TrafficControl):
    """tc qdisc 队列规则操作类"""

    def __init__(self):
        super().__init__()

    def build_parent_opt(self):
        """构建parent选项
        """
        if self._type == "ingress":
            return ""
        elif self._parent == "root":
            return " root"
        elif self._parent:
            return f" parent {self._parent}"
        else:
            return ""

    def build_handle_opt(self):
        """构建handle选项
        """
        return f" handle {self._handle}" if self._handle else ""

    def build_options_opt(self):
        """构建options选项
        """
        return f" {self._options}" if self._options else ""

    def add(self) -> Process:
        """添加队列规则

        tc qdisc add dev DEVICE [ root | parent CLASSID ] [ handle HANDLE ] QDISC_TYPE [ OPTIONS ]
        """
        if not self._device:
            raise ValueError("device is not set")
        if not self._type:
            raise ValueError("type is not set")

        parent_opt = self.build_parent_opt()
        handle_opt = self.build_handle_opt()
        options_opt = self.build_options_opt()

        # 检查是否已存在
        if self.check():
            return Process.from_init(args="qdisc already exists")

        cmd = f"{self._base} qdisc add dev {self._device}{parent_opt}{handle_opt} {self._type}{options_opt}"
        return self.exec(cmd)

    def delete(self) -> Process:
        """删除队列规则
        """
        if not self._device:
            raise ValueError("device is not set")
        if not self._type:
            raise ValueError("type is not set")

        parent_opt = self.build_parent_opt()
        handle_opt = self.build_handle_opt()

        # 检查是否存在
        if not self.check():
            return Process.from_init(args="qdisc not exists")

        cmd = f"{self._base} qdisc del dev {self._device}{parent_opt}{handle_opt} {self._type}"
        return self.exec(cmd)

    def show(self, *greps: Grep) -> Process:
        """显示队列规则
        """
        if not self._device:
            raise ValueError("device is not set")

        cmd = f"{self._base} qdisc show dev {self._device}"
        return self.exec(cmd, greps)

    def check(self) -> bool:
        """检查队列规则是否存在
        """
        if not self._device or not self._type:
            return False

        parent_opt = self.build_parent_opt()
        handle_part = self._handle if self._handle else ""

        grep = Grep().option("w").pattern(f"qdisc {self._type} {handle_part}{parent_opt}")
        return self.show(grep).is_successful()


class TcClass(TrafficControl):
    """tc class 类操作类"""

    def __init__(self):
        super().__init__()

    def build_parent_opt(self):
        """构建parent选项
        """
        return f"parent {self._parent}" if self._parent else ""

    def build_handle_opt(self):
        """构建classid选项
        """
        return f"classid {self._handle}" if self._handle else ""

    def build_rate_opt(self):
        """构建rate选项
        """
        if not self._rate:
            return ""
        rate = self._rate if self._rate.endswith("mbit") else f"{self._rate}mbit"
        return f"rate {rate}"

    def build_ceil_opt(self):
        """构建ceil选项
        """
        if not self._ceil:
            return ""
        ceil = self._ceil if self._ceil.endswith("mbit") else f"{self._ceil}mbit"
        return f"ceil {ceil}"

    def add(self) -> Process:
        """添加类

        tc class add dev DEV parent QDISC-ID classid CLASS-ID QDISC [ QDISC-SPECIFIC-PARAMETERS ]
        """
        if not self._device:
            raise ValueError("device is not set")
        if not self._parent:
            raise ValueError("parent is not set")
        if not self._handle:
            raise ValueError("handle is not set")
        if not self._type:
            raise ValueError("type is not set")

        # 检查是否已存在
        if self.check():
            return Process.from_init(args="class already exists")

        parent_opt = self.build_parent_opt()
        handle_opt = self.build_handle_opt()
        rate_opt = self.build_rate_opt()
        ceil_opt = self.build_ceil_opt()

        cmd = f"{self._base} class add dev {self._device} {parent_opt} {handle_opt} {self._type} {rate_opt} {ceil_opt}"
        return self.exec(cmd)

    def delete(self) -> Process:
        """删除类
        """
        if not self._device:
            raise ValueError("device is not set")
        if not self._handle:
            raise ValueError("handle is not set")

        # 检查是否存在
        if not self.check():
            return Process.from_init(args="class not exists")

        cmd = f"{self._base} class del dev {self._device} classid {self._handle}"
        return self.exec(cmd)

    def show(self, *greps: Grep) -> Process:
        """显示类
        """
        if not self._device:
            raise ValueError("device is not set")

        cmd = f"{self._base} class show dev {self._device}"
        return self.exec(cmd, greps)

    def check(self) -> bool:
        """检查类是否存在
        """
        if not self._device or not self._handle or not self._type:
            return False

        grep = Grep().option("w").pattern(f"class {self._type} {self._handle}")
        return self.show(grep).is_successful()


class TcFilter(TrafficControl):
    """tc filter 过滤器操作类"""

    def __init__(self):
        super().__init__()
        self._filter_type = "u32"  # 默认使用u32过滤器

    def filter_type(self, filter_type: str):
        """指定过滤器类型
        """
        self._filter_type = filter_type
        return self

    def u32(self):
        """指定u32过滤器
        """
        self._filter_type = "u32"
        return self

    def build_parent_opt(self):
        """构建parent选项
        """
        return f"parent {self._parent}" if self._parent else ""

    def build_protocol_opt(self):
        """构建protocol选项
        """
        protocol = self._protocol if self._protocol else "ip"
        return f"protocol {protocol}"

    def build_prio_opt(self):
        """构建prio选项
        """
        prio = self._prio if self._prio else 10
        return f"prio {prio}"

    def build_match_opt(self):
        """构建match选项
        """
        return f"match {self._match}" if self._match else ""

    def build_flowid_opt(self):
        """构建flowid选项
        """
        return f"flowid {self._flowid}" if self._flowid else ""

    def build_options_opt(self):
        """构建options选项
        """
        return self._options if self._options else ""

    def add(self) -> Process:
        """添加过滤器

        tc filter add dev DEV parent QDISC-ID protocol PROTO prio PRIORITY FILTER_TYPE [ FILTER_OPTIONS ] flowid CLASS_ID
        """
        if not self._device:
            raise ValueError("device is not set")
        if not self._parent:
            raise ValueError("parent is not set")
        if not self._match:
            raise ValueError("match is not set")

        # 检查是否已存在
        if self.check():
            return Process.from_init(args="filter already exists")

        parent_opt = self.build_parent_opt()
        protocol_opt = self.build_protocol_opt()
        prio_opt = self.build_prio_opt()
        match_opt = self.build_match_opt()
        flowid_opt = self.build_flowid_opt()
        options_opt = self.build_options_opt()

        cmd = f"{self._base} filter add dev {self._device} {parent_opt} {protocol_opt} {prio_opt} {self._filter_type} {match_opt} {options_opt} {flowid_opt}"
        return self.exec(cmd)

    def delete(self) -> Process:
        """删除过滤器
        """
        if not self._device:
            raise ValueError("device is not set")
        if not self._parent:
            raise ValueError("parent is not set")
        if not self._match:
            raise ValueError("match is not set")

        # 检查是否存在
        if not self.check():
            return Process.from_init(args="filter not exists")

        parent_opt = self.build_parent_opt()
        protocol_opt = self.build_protocol_opt()
        prio_opt = f"pref {self._prio if self._prio else 10}"
        match_opt = self.build_match_opt()

        cmd = f"{self._base} filter del dev {self._device} {protocol_opt} {parent_opt} {prio_opt} {self._filter_type} {match_opt}"
        return self.exec(cmd)

    def show(self, *greps: Grep) -> Process:
        """显示过滤器
        """
        if not self._device:
            raise ValueError("device is not set")

        cmd = f"{self._base} -p filter show dev {self._device}"
        return self.exec(cmd, greps)

    def check(self) -> bool:
        """检查过滤器是否存在
        """
        if not self._device or not self._match:
            return False

        grep = Grep().option("wi").pattern(f"match {self._match}")
        return self.show(grep).is_successful()


if __name__ == "__main__":
    # 示例用法
    print("=== NftCtl 类使用示例 ===")

    table = NftTable().ip().name("nat-2001")
    chain = NftChain().name("postrouting_sub").type("nat").postrouting().priority(101)
    sets = NftSet().name("allowed_ips").type("ipv4_addr").element("***********", "***********")
    rule = NftRule().saddr(NftMatch("**********/16")).daddr(NftMatch("**********/16")).counter().snat_to("**********")

    # 基本用法：创建表、链、规则
    nft = Nft().namespace("ns-vlan2001")
    # print("创建链:", nft.list_chain(table, chain).args)
    print("创建规则:", nft.del_rule(table, chain, rule))

    print("\n=== TrafficControl 类使用示例 ===")

    # 基本TC操作
    tc = TrafficControl().namespace("ns-vlan2001").device("eip-ns-2001")

    # 添加HTB根队列
    qdisc_result = tc.qdisc().root().handle("1:").htb().options("default 10").add()
    print("添加HTB根队列:", qdisc_result.args)

    # 添加HTB类
    class_result = tc.class_cmd().parent("1:").handle("1:1").htb().rate("100mbit").ceil("100mbit").add()
    print("添加HTB类:", class_result.args)

    # 添加过滤器
    filter_result = tc.filter_cmd().parent("1:").match("ip src ***********").flowid("1:1").add()
    print("添加过滤器:", filter_result.args)

    # 清理TC规则
    clear_result = tc.clear()
    print("清理TC规则:", clear_result.args)

    # print("\n=== 与现有类的集成 ===")
    # process = Ip().link().show()
    # print("IP 链路:", process.args)
