"""
Main agent class for v-switch.
"""

import logging
import os
from typing import Dict, Any
from v_switch.config.agent_config import AgentConfig
from v_switch.agent.database_heartbeat_manager import DatabaseHeartbeatManager
from v_switch.agent.operation_handler import OperationHandler
from v_switch.agent.http_server import AgentHttpServer
from v_switch.agent.local_data import LocalDataManager
from v_switch.agent.environment_checker import <PERSON><PERSON>hecker
from v_switch.agent.command_processor import CommandProcessor


class VSwitchAgent:
    """Main v-switch agent class."""

    # Data directory for local configs
    DATA_DIR = "./data/subnet"

    def __init__(self, config: AgentConfig, dry_run: bool = False):
        """初始化 agent.

        Args:
            config: Agent configuration
            dry_run: 如果为 True，则命令会被记录但不会执行
            api_server_url: API server URL for heartbeat
        """
        self.config = config
        self.dry_run = dry_run
        self.api_server_url = config.server.base_url
        self.logger = logging.getLogger(__name__)

        os.makedirs(self.DATA_DIR, exist_ok=True)

        # Initialize components
        self.local_data = LocalDataManager(self.DATA_DIR)
        self.env_checker = EnvironmentChecker(config.env_check)
        self.heartbeat_manager = DatabaseHeartbeatManager(config, self.api_server_url)
        self.command_processor = CommandProcessor(local_data=self.local_data, dry_run=dry_run)

        # Initialize operation handler and HTTP server
        self.logger.info("Initializing operation handler and HTTP server...")
        self.operation_handler = OperationHandler(self.heartbeat_manager, self.command_processor)
        self.http_server = AgentHttpServer(
            operation_handler=self.operation_handler,
            host=config.agent.api_host,
            port=config.agent.api_port
        )

        # State management
        self._running = False

    def start(self) -> bool:
        """启动 agent

        Returns:
            True if successful
        """
        try:
            self.logger.info("Starting v-switch agent...")

            # Check environment before starting
            self.logger.info("Checking environment...")
            env_ok, env_errors = self.env_checker.check_all()
            if not env_ok:
                self.logger.error(f"Environment check failed: {env_errors}")
                return False
            self.logger.info("Environment check passed")

            # Start HTTP server for receiving operations
            self.logger.info("Starting HTTP server...")
            if not self.http_server.start():
                self.logger.error("Failed to start HTTP server")
                return False

            # Start heartbeat manager (registers node and starts heartbeat)
            self.logger.info("Starting heartbeat manager...")
            if not self.heartbeat_manager.start():
                self.logger.error("Failed to start heartbeat manager")
                return False

            # Sync operations on startup
            self.logger.info("Syncing operations on startup...")
            if not self.operation_handler.sync_operations_on_startup():
                self.logger.warning("Failed to sync operations on startup, continuing anyway")

            self._running = True
            self.logger.info("V-switch agent started successfully")
            self.logger.info(f"Agent endpoint: {self.config.agent.endpoint}")
            self.logger.info(f"Agent node_id: {self.config.agent.node_id}")
            self.logger.info(f"Agent group_id: {self.config.agent.group_id}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to start agent: {e}")
            return False

    def stop(self) -> None:
        """停止 agent."""
        try:
            self.logger.info("Stopping v-switch agent...")
            self._running = False

            # Stop HTTP server
            self.http_server.stop()

            # Stop heartbeat manager
            self.heartbeat_manager.stop()

            self.logger.info("V-switch agent stopped")

        except Exception as e:
            self.logger.error(f"Error stopping agent: {e}")

    def is_running(self) -> bool:
        """检查agent是否正在运行。"""
        return self._running

    def get_status(self) -> Dict[str, Any]:
        """获取agent状态信息。"""
        return {
            'running': self._running,
            'node_id': self.config.agent.node_id,
            'group_id': self.config.agent.group_id,
            'endpoint': self.config.agent.endpoint,
            'last_applied': self.heartbeat_manager.get_last_applied() if self.heartbeat_manager else 0,
            'heartbeat_status': self.heartbeat_manager.get_current_status() if self.heartbeat_manager else 'unknown'
        }
