#!/usr/bin/env python3
"""
Demo script for the database migration functionality.
Shows how the new database-based system works.
"""

import sys
import os
import time
import logging

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from v_switch.db import init_db
from v_switch.db.redo_log import RedoLog, create_redo_log, get_redo_logs_after_id
from v_switch.db.node_status import NodeStatus, register_or_update_node, get_nodes_by_group_id
from v_switch.core_service.database_service import DatabaseService
from v_switch.core_service.database_agent_manager import DatabaseAgentManager


def setup_logging():
    """Setup logging for the demo."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def demo_database_operations():
    """Demonstrate database operations."""
    print("=== Database Operations Demo ===")
    
    # Initialize database
    print("1. Initializing database...")
    try:
        init_db()
        print("   ✓ Database initialized successfully")
    except Exception as e:
        print(f"   ✗ Database initialization failed: {e}")
        return False

    # Create database service
    db_service = DatabaseService()
    
    # Demo: Create subnet record
    print("\n2. Creating subnet record...")
    subnet_data = {
        'server_type': 'guest',
        'ip_start': '**************',
        'ip_end': '***************',
        'ip_gateway': '*************',
        'ip_mask': 24,
        'monitor_ip': '***********0',
        'monitor_gateway_ip': '***********'
    }
    
    try:
        subnet_id = db_service.create_subnet_record('demo-tenant', 1500, subnet_data)
        if subnet_id:
            print(f"   ✓ Created subnet record with ID: {subnet_id}")
        else:
            print("   ✗ Failed to create subnet record")
    except Exception as e:
        print(f"   ✗ Error creating subnet record: {e}")

    # Demo: Log operation
    print("\n3. Logging operation to redo_log...")
    operation_data = {
        'vlan_id': 1500,
        'tenant_id': 'demo-tenant',
        'action': 'create_subnet',
        'subnet_data': subnet_data
    }
    
    try:
        log_id = db_service.log_operation('create', 'network', operation_data)
        if log_id:
            print(f"   ✓ Logged operation with ID: {log_id}")
        else:
            print("   ✗ Failed to log operation")
    except Exception as e:
        print(f"   ✗ Error logging operation: {e}")

    return True


def demo_node_management():
    """Demonstrate node management operations."""
    print("\n=== Node Management Demo ===")
    
    # Register nodes
    print("1. Registering nodes...")
    nodes_to_register = [
        ('node-1', 'group-0', '192.168.1.100:8080'),
        ('node-2', 'group-0', '192.168.1.101:8080'),
        ('node-3', 'group-1', '192.168.1.102:8080'),
        ('node-4', 'group-1', '192.168.1.103:8080'),
    ]
    
    for node_id, group_id, endpoint in nodes_to_register:
        try:
            node = register_or_update_node(node_id, group_id, endpoint)
            if node:
                print(f"   ✓ Registered node {node_id} in {group_id}")
            else:
                print(f"   ✗ Failed to register node {node_id}")
        except Exception as e:
            print(f"   ✗ Error registering node {node_id}: {e}")

    # Get nodes by group
    print("\n2. Getting nodes by group...")
    for group_id in ['group-0', 'group-1']:
        try:
            nodes = get_nodes_by_group_id(group_id)
            print(f"   ✓ Found {len(nodes)} nodes in {group_id}:")
            for node in nodes:
                print(f"     - {node.node_id} ({node.endpoint}) - {node.status}")
        except Exception as e:
            print(f"   ✗ Error getting nodes for {group_id}: {e}")


def demo_agent_manager():
    """Demonstrate agent manager functionality."""
    print("\n=== Agent Manager Demo ===")
    
    agent_manager = DatabaseAgentManager()
    
    # Test group ID calculation
    print("1. Testing group ID calculation...")
    test_vlans = [1500, 1501, 1502, 1503, 1504]
    for vlan_id in test_vlans:
        group_id = agent_manager.get_group_id_for_vlan(vlan_id)
        print(f"   VLAN {vlan_id} → {group_id}")

    # Test getting agents for VLAN
    print("\n2. Getting agents for VLANs...")
    for vlan_id in [1500, 1501]:
        try:
            agents = agent_manager.get_agents_for_vlan(vlan_id)
            group_id = agent_manager.get_group_id_for_vlan(vlan_id)
            print(f"   VLAN {vlan_id} ({group_id}): {len(agents)} running agents")
            for agent in agents:
                print(f"     - {agent.node_id} at {agent.endpoint}")
        except Exception as e:
            print(f"   ✗ Error getting agents for VLAN {vlan_id}: {e}")


def demo_redo_log_sync():
    """Demonstrate redo log synchronization."""
    print("\n=== Redo Log Sync Demo ===")
    
    # Create some test redo log entries
    print("1. Creating test redo log entries...")
    test_operations = [
        ('create', 'network', {'vlan_id': 1500, 'action': 'create_subnet'}),
        ('create', 'eip_binding', {'vlan_id': 1500, 'eip': '**************', 'internal_ip': '**************'}),
        ('create', 'eip_snat', {'vlan_id': 1500, 'eip': '**************', 'gateway_ip': '*************'}),
        ('delete', 'eip_snat', {'vlan_id': 1500, 'eip': '**************'}),
    ]
    
    created_logs = []
    for op_type, obj_type, data in test_operations:
        try:
            redo_log = RedoLog(type=op_type, object=obj_type, data=data)
            created_log = create_redo_log(redo_log)
            if created_log:
                created_logs.append(created_log)
                print(f"   ✓ Created redo log {created_log.id}: {op_type} {obj_type}")
            else:
                print(f"   ✗ Failed to create redo log: {op_type} {obj_type}")
        except Exception as e:
            print(f"   ✗ Error creating redo log: {e}")

    # Simulate agent sync
    if created_logs:
        print(f"\n2. Simulating agent sync from log ID {created_logs[0].id - 1}...")
        try:
            sync_logs = get_redo_logs_after_id(created_logs[0].id - 1, limit=10)
            print(f"   ✓ Found {len(sync_logs)} logs to sync:")
            for log in sync_logs:
                print(f"     - Log {log.id}: {log.type} {log.object} (VLAN {log.data.get('vlan_id', 'N/A')})")
        except Exception as e:
            print(f"   ✗ Error getting sync logs: {e}")


def main():
    """Main demo function."""
    print("V-Switch Database Migration Demo")
    print("=" * 40)
    
    setup_logging()
    
    try:
        # Run demos
        if not demo_database_operations():
            print("Database operations demo failed, skipping other demos")
            return 1
        
        demo_node_management()
        demo_agent_manager()
        demo_redo_log_sync()
        
        print("\n" + "=" * 40)
        print("Demo completed successfully!")
        print("\nNext steps:")
        print("1. Start the API server with the new heartbeat endpoint")
        print("2. Start agents with the new configuration format")
        print("3. Test the full operation flow with real HTTP calls")
        
        return 0
        
    except Exception as e:
        print(f"\nDemo failed with error: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
