"""
Test cases for environment check optimization.
"""

import pytest
import time
from unittest.mock import Mock, patch, MagicMock
from v_switch.agent.agent import VSwitchAgent
from v_switch.agent.database_heartbeat_manager import DatabaseHeartbeatManager
from v_switch.config.agent_config import AgentConfig
from v_switch.agent.environment_checker import EnvironmentChecker


class TestEnvironmentCheckOptimization:
    """Test environment check optimization in agent startup and heartbeat."""

    def setup_method(self):
        """Set up test fixtures."""
        # Create mock config
        self.mock_config = Mock(spec=AgentConfig)
        self.mock_config.agent = Mock()
        self.mock_config.agent.node_id = "test-node-1"
        self.mock_config.agent.group_id = "group-0"
        self.mock_config.agent.endpoint = "*************:8080"
        self.mock_config.agent.api_host = "0.0.0.0"
        self.mock_config.agent.api_port = 8080
        self.mock_config.agent.heartbeat_interval = 1.0  # Short interval for testing
        self.mock_config.server = Mock()
        self.mock_config.server.base_url = "http://localhost:30090"
        self.mock_config.env_check = Mock()

    @patch('v_switch.agent.agent.DatabaseHeartbeatManager')
    @patch('v_switch.agent.agent.OperationHandler')
    @patch('v_switch.agent.agent.AgentHttpServer')
    @patch('v_switch.agent.agent.LocalDataManager')
    @patch('v_switch.agent.agent.EnvironmentChecker')
    @patch('v_switch.agent.agent.CommandProcessor')
    @patch('os.makedirs')
    def test_environment_check_only_on_startup(self, mock_makedirs, mock_command_processor,
                                              mock_env_checker, mock_local_data, mock_http_server,
                                              mock_operation_handler, mock_heartbeat_manager):
        """Test that environment check is only performed during startup, not during heartbeat."""
        
        # Mock successful environment check
        mock_env_checker.return_value.check_all.return_value = (True, [])
        
        # Mock successful component starts
        mock_http_server.return_value.start.return_value = True
        mock_heartbeat_manager.return_value.start.return_value = True
        mock_operation_handler.return_value.sync_operations_on_startup.return_value = True
        
        # Create and start agent
        agent = VSwitchAgent(self.mock_config)
        result = agent.start()
        
        # Verify startup success and environment check was called once
        assert result is True
        assert agent.is_running()
        mock_env_checker.return_value.check_all.assert_called_once()
        
        # Reset the mock to verify it's not called again
        mock_env_checker.return_value.check_all.reset_mock()
        
        # Simulate some time passing (heartbeat would occur)
        # The environment check should not be called again
        time.sleep(0.1)
        
        # Verify environment check was not called again
        mock_env_checker.return_value.check_all.assert_not_called()

    @patch('v_switch.agent.agent.DatabaseHeartbeatManager')
    @patch('v_switch.agent.agent.OperationHandler')
    @patch('v_switch.agent.agent.AgentHttpServer')
    @patch('v_switch.agent.agent.LocalDataManager')
    @patch('v_switch.agent.agent.EnvironmentChecker')
    @patch('v_switch.agent.agent.CommandProcessor')
    @patch('os.makedirs')
    def test_startup_fails_on_environment_check_failure(self, mock_makedirs, mock_command_processor,
                                                       mock_env_checker, mock_local_data, mock_http_server,
                                                       mock_operation_handler, mock_heartbeat_manager):
        """Test that agent startup fails when environment check fails."""
        
        # Mock environment check failure
        mock_env_checker.return_value.check_all.return_value = (False, ["OVS not found", "nftables missing"])
        
        # Create and start agent
        agent = VSwitchAgent(self.mock_config)
        result = agent.start()
        
        # Verify startup failure
        assert result is False
        assert not agent.is_running()
        
        # Verify environment check was called
        mock_env_checker.return_value.check_all.assert_called_once()
        
        # Verify other components were not started
        mock_http_server.return_value.start.assert_not_called()
        mock_heartbeat_manager.return_value.start.assert_not_called()
        mock_operation_handler.return_value.sync_operations_on_startup.assert_not_called()

    def test_heartbeat_manager_initialization(self):
        """Test that heartbeat manager initializes with correct status."""
        
        # Create mock environment checker
        mock_env_checker = Mock(spec=EnvironmentChecker)
        
        # Create heartbeat manager
        heartbeat_manager = DatabaseHeartbeatManager(
            config=self.mock_config,
            env_checker=mock_env_checker,
            api_server_url="http://localhost:30090"
        )
        
        # Verify initial status
        assert heartbeat_manager.get_current_status() == "unknown"
        assert heartbeat_manager.get_last_applied() == 0

    @patch('requests.post')
    @patch('threading.Thread')
    def test_heartbeat_manager_start_sets_running_status(self, mock_thread, mock_post):
        """Test that heartbeat manager sets status to running on start."""
        
        # Create mock environment checker
        mock_env_checker = Mock(spec=EnvironmentChecker)
        
        # Create heartbeat manager
        heartbeat_manager = DatabaseHeartbeatManager(
            config=self.mock_config,
            env_checker=mock_env_checker,
            api_server_url="http://localhost:30090"
        )
        
        # Start heartbeat manager
        result = heartbeat_manager.start()
        
        # Verify start success and status is set to running
        assert result is True
        assert heartbeat_manager.get_current_status() == "running"
        
        # Verify thread was started
        mock_thread.assert_called_once()
        mock_thread.return_value.start.assert_called_once()

    @patch('requests.post')
    def test_heartbeat_loop_does_not_check_environment(self, mock_post):
        """Test that heartbeat loop does not perform environment checks."""
        
        # Mock successful HTTP response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_post.return_value = mock_response
        
        # Create mock environment checker
        mock_env_checker = Mock(spec=EnvironmentChecker)
        
        # Create heartbeat manager
        heartbeat_manager = DatabaseHeartbeatManager(
            config=self.mock_config,
            env_checker=mock_env_checker,
            api_server_url="http://localhost:30090"
        )
        
        # Manually call _send_heartbeat to simulate heartbeat loop
        result = heartbeat_manager._send_heartbeat()
        
        # Verify heartbeat was sent successfully
        assert result is True
        mock_post.assert_called_once()
        
        # Verify environment checker was not called
        mock_env_checker.check_all.assert_not_called()

    def test_environment_check_error_messages(self):
        """Test that environment check errors are properly logged."""
        
        with patch('v_switch.agent.agent.DatabaseHeartbeatManager'), \
             patch('v_switch.agent.agent.OperationHandler'), \
             patch('v_switch.agent.agent.AgentHttpServer'), \
             patch('v_switch.agent.agent.LocalDataManager'), \
             patch('v_switch.agent.agent.EnvironmentChecker') as mock_env_checker, \
             patch('v_switch.agent.agent.CommandProcessor'), \
             patch('os.makedirs'):
            
            # Mock environment check failure with specific errors
            mock_env_checker.return_value.check_all.return_value = (
                False, 
                ["OVS bridge not found", "nftables command not available", "Insufficient permissions"]
            )
            
            # Create agent
            agent = VSwitchAgent(self.mock_config)
            
            # Capture log messages
            with patch.object(agent.logger, 'error') as mock_log_error:
                result = agent.start()
                
                # Verify startup failed
                assert result is False
                
                # Verify error was logged with environment check details
                mock_log_error.assert_called()
                error_call = mock_log_error.call_args[0][0]
                assert "Environment check failed" in error_call
                assert "OVS bridge not found" in str(error_call)


if __name__ == '__main__':
    pytest.main([__file__])
