"""
Test cases for the migrated agent without etcd dependencies.
"""

import pytest
import time
from unittest.mock import Mock, patch, MagicMock
from v_switch.agent.agent import VSwitchAgent
from v_switch.config.agent_config import AgentConfig


class TestAgentMigration:
    """Test the migrated agent functionality."""

    def setup_method(self):
        """Set up test fixtures."""
        # Create mock config
        self.mock_config = Mock(spec=AgentConfig)
        self.mock_config.agent = Mock()
        self.mock_config.agent.node_id = "test-node-1"
        self.mock_config.agent.group_id = "group-0"
        self.mock_config.agent.endpoint = "*************:8080"
        self.mock_config.agent.api_host = "0.0.0.0"
        self.mock_config.agent.api_port = 8080
        self.mock_config.agent.heartbeat_interval = 30.0
        self.mock_config.server = Mock()
        self.mock_config.server.base_url = "http://localhost:30090"
        self.mock_config.env_check = Mock()

    @patch('v_switch.agent.agent.DatabaseHeartbeatManager')
    @patch('v_switch.agent.agent.OperationHandler')
    @patch('v_switch.agent.agent.AgentHttpServer')
    @patch('v_switch.agent.agent.LocalDataManager')
    @patch('v_switch.agent.agent.EnvironmentChecker')
    @patch('v_switch.agent.agent.CommandProcessor')
    @patch('os.makedirs')
    def test_agent_initialization(self, mock_makedirs, mock_command_processor,
                                  mock_env_checker, mock_local_data, mock_http_server,
                                  mock_operation_handler, mock_heartbeat_manager):
        """Test agent initialization without etcd."""

        # Create agent
        agent = VSwitchAgent(
            config=self.mock_config,
            dry_run=False
        )

        # Verify initialization
        assert agent.config == self.mock_config
        assert agent.dry_run is False
        assert agent.api_server_url == "http://localhost:30090"
        assert not agent.is_running()

        # Verify components were created
        mock_makedirs.assert_called_once_with("./data/subnet", exist_ok=True)
        mock_local_data.assert_called_once_with("./data/subnet")
        mock_env_checker.assert_called_once_with(self.mock_config.env_check)
        mock_heartbeat_manager.assert_called_once_with(
            self.mock_config, mock_env_checker.return_value, "http://localhost:30090"
        )
        mock_command_processor.assert_called_once_with(
            local_data=mock_local_data.return_value, dry_run=False
        )
        mock_operation_handler.assert_called_once_with(
            mock_heartbeat_manager.return_value, mock_command_processor.return_value
        )
        mock_http_server.assert_called_once_with(
            operation_handler=mock_operation_handler.return_value,
            host='0.0.0.0',
            port=8080
        )

    @patch('v_switch.agent.agent.DatabaseHeartbeatManager')
    @patch('v_switch.agent.agent.OperationHandler')
    @patch('v_switch.agent.agent.AgentHttpServer')
    @patch('v_switch.agent.agent.LocalDataManager')
    @patch('v_switch.agent.agent.EnvironmentChecker')
    @patch('v_switch.agent.agent.CommandProcessor')
    @patch('os.makedirs')
    def test_agent_start_success(self, mock_makedirs, mock_command_processor,
                                 mock_env_checker, mock_local_data, mock_http_server,
                                 mock_operation_handler, mock_heartbeat_manager):
        """Test successful agent startup."""

        # Mock successful environment check
        mock_env_checker.return_value.check_all.return_value = (True, [])

        # Mock successful component starts
        mock_http_server.return_value.start.return_value = True
        mock_heartbeat_manager.return_value.start.return_value = True
        mock_operation_handler.return_value.sync_operations_on_startup.return_value = True

        # Create and start agent
        agent = VSwitchAgent(self.mock_config)
        result = agent.start()

        # Verify startup sequence
        assert result is True
        assert agent.is_running()

        # Verify environment check was called
        mock_env_checker.return_value.check_all.assert_called_once()
        mock_http_server.return_value.start.assert_called_once()
        mock_heartbeat_manager.return_value.start.assert_called_once()
        mock_operation_handler.return_value.sync_operations_on_startup.assert_called_once()

    @patch('v_switch.agent.agent.DatabaseHeartbeatManager')
    @patch('v_switch.agent.agent.OperationHandler')
    @patch('v_switch.agent.agent.AgentHttpServer')
    @patch('v_switch.agent.agent.LocalDataManager')
    @patch('v_switch.agent.agent.EnvironmentChecker')
    @patch('v_switch.agent.agent.CommandProcessor')
    @patch('os.makedirs')
    def test_agent_start_environment_check_failure(self, mock_makedirs, mock_command_processor,
                                                   mock_env_checker, mock_local_data, mock_http_server,
                                                   mock_operation_handler, mock_heartbeat_manager):
        """Test agent startup failure when environment check fails."""

        # Mock environment check failure
        mock_env_checker.return_value.check_all.return_value = (False, ["OVS not found", "nftables not available"])

        # Create and start agent
        agent = VSwitchAgent(self.mock_config)
        result = agent.start()

        # Verify startup failure
        assert result is False
        assert not agent.is_running()

        # Verify environment check was called but other components were not started
        mock_env_checker.return_value.check_all.assert_called_once()
        mock_http_server.return_value.start.assert_not_called()
        mock_heartbeat_manager.return_value.start.assert_not_called()

    @patch('v_switch.agent.agent.DatabaseHeartbeatManager')
    @patch('v_switch.agent.agent.OperationHandler')
    @patch('v_switch.agent.agent.AgentHttpServer')
    @patch('v_switch.agent.agent.LocalDataManager')
    @patch('v_switch.agent.agent.EnvironmentChecker')
    @patch('v_switch.agent.agent.CommandProcessor')
    @patch('os.makedirs')
    def test_agent_start_http_server_failure(self, mock_makedirs, mock_command_processor,
                                             mock_env_checker, mock_local_data, mock_http_server,
                                             mock_operation_handler, mock_heartbeat_manager):
        """Test agent startup failure when HTTP server fails to start."""

        # Mock successful environment check but HTTP server start failure
        mock_env_checker.return_value.check_all.return_value = (True, [])
        mock_http_server.return_value.start.return_value = False

        # Create and start agent
        agent = VSwitchAgent(self.mock_config)
        result = agent.start()

        # Verify startup failure
        assert result is False
        assert not agent.is_running()

        mock_env_checker.return_value.check_all.assert_called_once()
        mock_http_server.return_value.start.assert_called_once()
        mock_heartbeat_manager.return_value.start.assert_not_called()

    @patch('v_switch.agent.agent.DatabaseHeartbeatManager')
    @patch('v_switch.agent.agent.OperationHandler')
    @patch('v_switch.agent.agent.AgentHttpServer')
    @patch('v_switch.agent.agent.LocalDataManager')
    @patch('v_switch.agent.agent.EnvironmentChecker')
    @patch('v_switch.agent.agent.CommandProcessor')
    @patch('os.makedirs')
    def test_agent_start_heartbeat_failure(self, mock_makedirs, mock_command_processor,
                                           mock_env_checker, mock_local_data, mock_http_server,
                                           mock_operation_handler, mock_heartbeat_manager):
        """Test agent startup failure when heartbeat manager fails to start."""

        # Mock successful environment check and HTTP server start but heartbeat failure
        mock_env_checker.return_value.check_all.return_value = (True, [])
        mock_http_server.return_value.start.return_value = True
        mock_heartbeat_manager.return_value.start.return_value = False

        # Create and start agent
        agent = VSwitchAgent(self.mock_config)
        result = agent.start()

        # Verify startup failure
        assert result is False
        assert not agent.is_running()

        mock_env_checker.return_value.check_all.assert_called_once()
        mock_http_server.return_value.start.assert_called_once()
        mock_heartbeat_manager.return_value.start.assert_called_once()

    @patch('v_switch.agent.agent.DatabaseHeartbeatManager')
    @patch('v_switch.agent.agent.OperationHandler')
    @patch('v_switch.agent.agent.AgentHttpServer')
    @patch('v_switch.agent.agent.LocalDataManager')
    @patch('v_switch.agent.agent.EnvironmentChecker')
    @patch('v_switch.agent.agent.CommandProcessor')
    @patch('os.makedirs')
    def test_agent_start_sync_failure_continues(self, mock_makedirs, mock_command_processor,
                                                mock_env_checker, mock_local_data, mock_http_server,
                                                mock_operation_handler, mock_heartbeat_manager):
        """Test agent startup continues even if sync fails."""

        # Mock successful environment check and starts but sync failure
        mock_env_checker.return_value.check_all.return_value = (True, [])
        mock_http_server.return_value.start.return_value = True
        mock_heartbeat_manager.return_value.start.return_value = True
        mock_operation_handler.return_value.sync_operations_on_startup.return_value = False

        # Create and start agent
        agent = VSwitchAgent(self.mock_config)
        result = agent.start()

        # Verify startup continues despite sync failure
        assert result is True
        assert agent.is_running()

        mock_env_checker.return_value.check_all.assert_called_once()
        mock_operation_handler.return_value.sync_operations_on_startup.assert_called_once()

    @patch('v_switch.agent.agent.DatabaseHeartbeatManager')
    @patch('v_switch.agent.agent.OperationHandler')
    @patch('v_switch.agent.agent.AgentHttpServer')
    @patch('v_switch.agent.agent.LocalDataManager')
    @patch('v_switch.agent.agent.EnvironmentChecker')
    @patch('v_switch.agent.agent.CommandProcessor')
    @patch('os.makedirs')
    def test_agent_stop(self, mock_makedirs, mock_command_processor,
                        mock_env_checker, mock_local_data, mock_http_server,
                        mock_operation_handler, mock_heartbeat_manager):
        """Test agent stop functionality."""

        # Mock successful environment check and component starts
        mock_env_checker.return_value.check_all.return_value = (True, [])
        mock_http_server.return_value.start.return_value = True
        mock_heartbeat_manager.return_value.start.return_value = True
        mock_operation_handler.return_value.sync_operations_on_startup.return_value = True

        # Create, start, and stop agent
        agent = VSwitchAgent(self.mock_config)
        agent.start()
        assert agent.is_running()

        agent.stop()

        # Verify stop sequence
        assert not agent.is_running()
        mock_http_server.return_value.stop.assert_called_once()
        mock_heartbeat_manager.return_value.stop.assert_called_once()

    @patch('v_switch.agent.agent.DatabaseHeartbeatManager')
    @patch('v_switch.agent.agent.OperationHandler')
    @patch('v_switch.agent.agent.AgentHttpServer')
    @patch('v_switch.agent.agent.LocalDataManager')
    @patch('v_switch.agent.agent.EnvironmentChecker')
    @patch('v_switch.agent.agent.CommandProcessor')
    @patch('os.makedirs')
    def test_agent_get_status(self, mock_makedirs, mock_command_processor,
                              mock_env_checker, mock_local_data, mock_http_server,
                              mock_operation_handler, mock_heartbeat_manager):
        """Test agent status retrieval."""

        # Mock heartbeat manager responses
        mock_heartbeat_manager.return_value.get_last_applied.return_value = 42
        mock_heartbeat_manager.return_value.get_current_status.return_value = "running"

        # Create agent
        agent = VSwitchAgent(self.mock_config)
        status = agent.get_status()

        # Verify status
        expected_status = {
            'running': False,  # Agent not started yet
            'node_id': 'test-node-1',
            'group_id': 'group-0',
            'endpoint': '*************:8080',
            'last_applied': 42,
            'heartbeat_status': 'running'
        }
        assert status == expected_status

    def test_agent_endpoint_parsing(self):
        """Test endpoint parsing for HTTP server port."""
        # Test with port in endpoint
        config_with_port = Mock(spec=AgentConfig)
        config_with_port.agent = Mock()
        config_with_port.agent.node_id = "test-node"
        config_with_port.agent.group_id = "group-0"
        config_with_port.agent.endpoint = "*************:9090"
        config_with_port.agent.api_host = "0.0.0.0"
        config_with_port.agent.api_port = 9090
        config_with_port.server = Mock()
        config_with_port.server.base_url = "http://localhost:30090"
        config_with_port.env_check = Mock()

        with patch('v_switch.agent.agent.DatabaseHeartbeatManager'), \
                patch('v_switch.agent.agent.OperationHandler'), \
                patch('v_switch.agent.agent.AgentHttpServer') as mock_http_server, \
                patch('v_switch.agent.agent.LocalDataManager'), \
                patch('v_switch.agent.agent.EnvironmentChecker'), \
                patch('v_switch.agent.agent.CommandProcessor'), \
                patch('os.makedirs'):

            agent = VSwitchAgent(config_with_port)

            # Verify HTTP server was created with correct port
            mock_http_server.assert_called_once()
            call_args = mock_http_server.call_args
            assert call_args[1]['port'] == 9090

        # Test with no port in endpoint (default to 8080)
        config_no_port = Mock(spec=AgentConfig)
        config_no_port.agent = Mock()
        config_no_port.agent.node_id = "test-node"
        config_no_port.agent.group_id = "group-0"
        config_no_port.agent.endpoint = "*************"
        config_no_port.agent.api_host = "0.0.0.0"
        config_no_port.agent.api_port = 8080
        config_no_port.server = Mock()
        config_no_port.server.base_url = "http://localhost:30090"
        config_no_port.env_check = Mock()

        with patch('v_switch.agent.agent.DatabaseHeartbeatManager'), \
                patch('v_switch.agent.agent.OperationHandler'), \
                patch('v_switch.agent.agent.AgentHttpServer') as mock_http_server, \
                patch('v_switch.agent.agent.LocalDataManager'), \
                patch('v_switch.agent.agent.EnvironmentChecker'), \
                patch('v_switch.agent.agent.CommandProcessor'), \
                patch('os.makedirs'):

            agent = VSwitchAgent(config_no_port)

            # Verify HTTP server was created with default port
            mock_http_server.assert_called_once()
            call_args = mock_http_server.call_args
            assert call_args[1]['port'] == 8080


if __name__ == '__main__':
    pytest.main([__file__])
