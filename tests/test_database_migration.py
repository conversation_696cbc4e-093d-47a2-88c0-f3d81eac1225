"""
Test cases for database migration functionality.
"""

import pytest
import time
from unittest.mock import Mock, patch
from v_switch.db.redo_log import Redo<PERSON><PERSON>, create_redo_log, get_redo_logs_after_id, get_latest_redo_log_id
from v_switch.db.node_status import Node<PERSON>tatus, register_or_update_node, update_node_heartbeat, get_nodes_by_group_id
from v_switch.core_service.database_service import DatabaseService
from v_switch.core_service.database_agent_manager import DatabaseAgentManager


class TestRedoLog:
    """Test redo log functionality."""

    def test_create_redo_log(self):
        """Test creating a redo log entry."""
        # This would require a test database setup
        # For now, we'll test the model structure
        redo_log = RedoLog(
            type='create',
            object='network',
            data={'vlan_id': 100, 'tenant_id': 'test-tenant'}
        )
        
        assert redo_log.type == 'create'
        assert redo_log.object == 'network'
        assert redo_log.data['vlan_id'] == 100

    def test_redo_log_timestamp(self):
        """Test that redo log gets proper timestamp."""
        current_time = int(time.time() * 1000)
        redo_log = RedoLog(
            type='update',
            object='eip_binding',
            data={'eip': '***********00'}
        )
        
        # The timestamp should be set automatically
        assert hasattr(redo_log, 'time')


class TestNodeStatus:
    """Test node status functionality."""

    def test_node_status_creation(self):
        """Test creating a node status entry."""
        node = NodeStatus(
            node_id='test-node-1',
            group_id='group-0',
            endpoint='***********00:8080',
            role='follower',
            status='running'
        )
        
        assert node.node_id == 'test-node-1'
        assert node.group_id == 'group-0'
        assert node.role == 'follower'
        assert node.status == 'running'

    def test_node_defaults(self):
        """Test node status default values."""
        node = NodeStatus(
            node_id='test-node-2',
            group_id='group-1',
            endpoint='*************:8080'
        )
        
        assert node.role == 'follower'
        assert node.status == 'running'
        assert node.last_applied == 0


class TestDatabaseService:
    """Test database service functionality."""

    def setUp(self):
        """Set up test fixtures."""
        self.db_service = DatabaseService()

    def test_database_service_init(self):
        """Test database service initialization."""
        db_service = DatabaseService()
        assert db_service is not None
        assert hasattr(db_service, 'logger')

    @patch('v_switch.core_service.database_service.create_network')
    def test_create_subnet_record(self, mock_create_network):
        """Test creating subnet record."""
        # Mock the database call
        mock_network = Mock()
        mock_network.id = 123
        mock_create_network.return_value = mock_network

        db_service = DatabaseService()
        subnet_data = {
            'server_type': 'guest',
            'ip_start': '***********0',
            'ip_end': '***********00',
            'ip_gateway': '***********',
            'ip_mask': 24
        }

        result = db_service.create_subnet_record('tenant-1', 100, subnet_data)
        assert result == 123
        mock_create_network.assert_called_once()

    @patch('v_switch.core_service.database_service.create_redo_log')
    def test_log_operation(self, mock_create_redo_log):
        """Test logging operation to redo log."""
        # Mock the database call
        mock_log = Mock()
        mock_log.id = 456
        mock_create_redo_log.return_value = mock_log

        db_service = DatabaseService()
        operation_data = {
            'vlan_id': 100,
            'tenant_id': 'tenant-1',
            'action': 'create_subnet'
        }

        result = db_service.log_operation('create', 'network', operation_data)
        assert result == 456
        mock_create_redo_log.assert_called_once()


class TestDatabaseAgentManager:
    """Test database agent manager functionality."""

    def test_get_group_id_for_vlan(self):
        """Test group ID calculation for VLAN."""
        agent_manager = DatabaseAgentManager()
        
        # Test with default 4 groups
        assert agent_manager.get_group_id_for_vlan(100) == 'group-0'  # 100 % 4 = 0
        assert agent_manager.get_group_id_for_vlan(101) == 'group-1'  # 101 % 4 = 1
        assert agent_manager.get_group_id_for_vlan(102) == 'group-2'  # 102 % 4 = 2
        assert agent_manager.get_group_id_for_vlan(103) == 'group-3'  # 103 % 4 = 3
        assert agent_manager.get_group_id_for_vlan(104) == 'group-0'  # 104 % 4 = 0

    def test_get_group_id_for_vlan_custom_groups(self):
        """Test group ID calculation with custom group count."""
        agent_manager = DatabaseAgentManager()
        
        # Test with 8 groups
        assert agent_manager.get_group_id_for_vlan(100, 8) == 'group-4'  # 100 % 8 = 4
        assert agent_manager.get_group_id_for_vlan(101, 8) == 'group-5'  # 101 % 8 = 5

    @patch('v_switch.core_service.database_agent_manager.get_nodes_by_group_id')
    def test_get_agents_for_vlan(self, mock_get_nodes):
        """Test getting agents for a VLAN."""
        # Mock database response
        mock_agent1 = Mock()
        mock_agent1.node_id = 'node-1'
        mock_agent1.status = 'running'
        mock_agent1.endpoint = '***********00:8080'
        
        mock_agent2 = Mock()
        mock_agent2.node_id = 'node-2'
        mock_agent2.status = 'error'
        mock_agent2.endpoint = '*************:8080'
        
        mock_get_nodes.return_value = [mock_agent1, mock_agent2]

        agent_manager = DatabaseAgentManager()
        agents = agent_manager.get_agents_for_vlan(100)

        # Should only return running agents
        assert len(agents) == 1
        assert agents[0].node_id == 'node-1'
        mock_get_nodes.assert_called_once_with('group-0')

    @patch('requests.post')
    def test_call_agent_operation(self, mock_post):
        """Test calling agent operation via HTTP."""
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_post.return_value = mock_response

        agent_manager = DatabaseAgentManager()
        operation_data = {
            'operation_type': 'create',
            'object_type': 'network',
            'vlan_id': 100
        }

        result = agent_manager.call_agent_operation(
            '***********00:8080', 
            'execute', 
            operation_data
        )

        assert result is True
        mock_post.assert_called_once()
        
        # Check the URL was constructed correctly
        call_args = mock_post.call_args
        assert call_args[1]['json'] == operation_data
        assert 'http://***********00:8080/api/operation/execute' in call_args[0]

    @patch('requests.post')
    def test_call_agent_operation_failure(self, mock_post):
        """Test agent operation call failure."""
        # Mock failed response
        mock_response = Mock()
        mock_response.status_code = 500
        mock_post.return_value = mock_response

        agent_manager = DatabaseAgentManager()
        result = agent_manager.call_agent_operation(
            '***********00:8080', 
            'execute', 
            {}
        )

        assert result is False


class TestIntegration:
    """Integration tests for the migration functionality."""

    @patch('v_switch.core_service.database_service.create_redo_log')
    @patch('v_switch.core_service.database_agent_manager.get_nodes_by_group_id')
    @patch('requests.post')
    def test_full_operation_flow(self, mock_post, mock_get_nodes, mock_create_log):
        """Test the full operation flow from database to agent notification."""
        # Setup mocks
        mock_log = Mock()
        mock_log.id = 789
        mock_create_log.return_value = mock_log

        mock_agent = Mock()
        mock_agent.node_id = 'node-1'
        mock_agent.status = 'running'
        mock_agent.endpoint = '***********00:8080'
        mock_get_nodes.return_value = [mock_agent]

        mock_response = Mock()
        mock_response.status_code = 200
        mock_post.return_value = mock_response

        # Test the flow
        db_service = DatabaseService()
        agent_manager = DatabaseAgentManager()

        # 1. Log operation
        operation_data = {'vlan_id': 100, 'action': 'create_subnet'}
        log_id = db_service.log_operation('create', 'network', operation_data)
        assert log_id == 789

        # 2. Notify agents
        success = agent_manager.notify_agents_of_operation(
            vlan_id=100,
            operation_type='create',
            object_type='network',
            data=operation_data,
            redo_log_id=log_id
        )
        assert success is True

        # Verify all mocks were called
        mock_create_log.assert_called_once()
        mock_get_nodes.assert_called_once_with('group-0')
        mock_post.assert_called_once()


if __name__ == '__main__':
    pytest.main([__file__])
