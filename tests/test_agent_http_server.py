"""
Test cases for the FastAPI-based agent HTTP server.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock
from fastapi.testclient import TestClient
from v_switch.agent.http_server import AgentHttpServer, OperationRequest, SuccessResponse, HealthResponse, StatusResponse


class TestAgentHttpServer:
    """Test the FastAPI-based agent HTTP server."""

    def setup_method(self):
        """Set up test fixtures."""
        # Mock operation handler
        self.mock_operation_handler = Mock()
        self.mock_heartbeat_manager = Mock()
        self.mock_operation_handler.heartbeat_manager = self.mock_heartbeat_manager
        
        # Create server instance
        self.server = AgentHttpServer(
            operation_handler=self.mock_operation_handler,
            host='127.0.0.1',
            port=8080
        )
        
        # Create test client
        self.client = TestClient(self.server.app)

    def test_health_endpoint(self):
        """Test the health check endpoint."""
        response = self.client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["status"] == "healthy"
        assert data["message"] == "Agent is running"

    def test_execute_operation_success(self):
        """Test successful operation execution."""
        # Mock successful operation handling
        self.mock_operation_handler.handle_operation.return_value = True
        
        operation_request = {
            "operation_type": "create",
            "object_type": "network",
            "redo_log_id": 123,
            "vlan_id": 1500,
            "data": {"tenant_id": "test-tenant"}
        }
        
        response = self.client.post("/api/operation/execute", json=operation_request)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["message"] == "Operation executed successfully"
        
        # Verify operation handler was called
        self.mock_operation_handler.handle_operation.assert_called_once()
        call_args = self.mock_operation_handler.handle_operation.call_args[0][0]
        assert call_args["operation_type"] == "create"
        assert call_args["object_type"] == "network"
        assert call_args["redo_log_id"] == 123

    def test_execute_operation_failure(self):
        """Test failed operation execution."""
        # Mock failed operation handling
        self.mock_operation_handler.handle_operation.return_value = False
        
        operation_request = {
            "operation_type": "delete",
            "object_type": "eip_binding",
            "redo_log_id": 456
        }
        
        response = self.client.post("/api/operation/execute", json=operation_request)
        
        assert response.status_code == 500
        data = response.json()
        assert "Failed to execute operation" in data["detail"]

    def test_execute_operation_exception(self):
        """Test operation execution with exception."""
        # Mock exception in operation handling
        self.mock_operation_handler.handle_operation.side_effect = Exception("Test error")
        
        operation_request = {
            "operation_type": "update",
            "object_type": "eip_snat",
            "redo_log_id": 789
        }
        
        response = self.client.post("/api/operation/execute", json=operation_request)
        
        assert response.status_code == 500
        data = response.json()
        assert "Internal server error" in data["detail"]
        assert "Test error" in data["detail"]

    def test_execute_operation_invalid_request(self):
        """Test operation execution with invalid request."""
        # Missing required fields
        invalid_request = {
            "operation_type": "create"
            # Missing object_type and redo_log_id
        }
        
        response = self.client.post("/api/operation/execute", json=invalid_request)
        
        assert response.status_code == 422  # Validation error

    def test_sync_operations_success(self):
        """Test successful operation sync."""
        # Mock successful sync
        self.mock_operation_handler.sync_operations_on_startup.return_value = True
        
        response = self.client.post("/api/sync")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["message"] == "Operations synced successfully"
        
        # Verify sync was called
        self.mock_operation_handler.sync_operations_on_startup.assert_called_once()

    def test_sync_operations_failure(self):
        """Test failed operation sync."""
        # Mock failed sync
        self.mock_operation_handler.sync_operations_on_startup.return_value = False
        
        response = self.client.post("/api/sync")
        
        assert response.status_code == 500
        data = response.json()
        assert "Failed to sync operations" in data["detail"]

    def test_get_status(self):
        """Test getting agent status."""
        # Mock heartbeat manager responses
        self.mock_heartbeat_manager.get_current_status.return_value = "running"
        self.mock_heartbeat_manager.get_last_applied.return_value = 42
        self.mock_heartbeat_manager.is_running.return_value = True
        
        response = self.client.get("/api/status")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["status"] == "running"
        assert data["last_applied"] == 42
        assert data["running"] is True

    def test_get_status_exception(self):
        """Test getting status with exception."""
        # Mock exception in status retrieval
        self.mock_heartbeat_manager.get_current_status.side_effect = Exception("Status error")
        
        response = self.client.get("/api/status")
        
        assert response.status_code == 500
        data = response.json()
        assert "Internal server error" in data["detail"]
        assert "Status error" in data["detail"]

    def test_server_initialization(self):
        """Test server initialization."""
        server = AgentHttpServer(
            operation_handler=self.mock_operation_handler,
            host='0.0.0.0',
            port=9090
        )
        
        assert server.host == '0.0.0.0'
        assert server.port == 9090
        assert server.operation_handler == self.mock_operation_handler
        assert server.app is not None
        assert not server.is_running()

    def test_get_endpoint(self):
        """Test getting server endpoint."""
        server = AgentHttpServer(
            operation_handler=self.mock_operation_handler,
            host='*************',
            port=8080
        )
        
        endpoint = server.get_endpoint()
        assert endpoint == "*************:8080"

    def test_operation_request_model(self):
        """Test the OperationRequest model."""
        # Test with all fields
        request_data = {
            "operation_type": "create",
            "object_type": "network",
            "redo_log_id": 123,
            "vlan_id": 1500,
            "data": {"tenant_id": "test"}
        }
        
        request = OperationRequest(**request_data)
        assert request.operation_type == "create"
        assert request.object_type == "network"
        assert request.redo_log_id == 123
        assert request.vlan_id == 1500
        assert request.data == {"tenant_id": "test"}

    def test_operation_request_model_minimal(self):
        """Test the OperationRequest model with minimal fields."""
        # Test with required fields only
        request_data = {
            "operation_type": "delete",
            "object_type": "eip_binding",
            "redo_log_id": 456
        }
        
        request = OperationRequest(**request_data)
        assert request.operation_type == "delete"
        assert request.object_type == "eip_binding"
        assert request.redo_log_id == 456
        assert request.vlan_id is None
        assert request.data is None

    def test_response_models(self):
        """Test response models."""
        # Test SuccessResponse
        success_response = SuccessResponse(message="Test message")
        assert success_response.success is True
        assert success_response.message == "Test message"
        assert success_response.data is None

        # Test HealthResponse
        health_response = HealthResponse()
        assert health_response.success is True
        assert health_response.status == "healthy"
        assert health_response.message == "Agent is running"

        # Test StatusResponse
        status_response = StatusResponse(
            status="running",
            last_applied=100,
            running=True
        )
        assert status_response.success is True
        assert status_response.status == "running"
        assert status_response.last_applied == 100
        assert status_response.running is True


if __name__ == '__main__':
    pytest.main([__file__])
