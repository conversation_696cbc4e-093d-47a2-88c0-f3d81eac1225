#!/usr/bin/env python3
"""
Example of using the FastAPI-based agent HTTP server.
"""

import sys
import os
import time
import logging
import requests
from unittest.mock import Mock

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from v_switch.agent.http_server import AgentHttpServer
from v_switch.agent.operation_handler import OperationHandler
from v_switch.agent.database_heartbeat_manager import DatabaseHeartbeatManager
from v_switch.config.agent_config import AgentConfig


def setup_logging():
    """Setup logging for the example."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def create_mock_components():
    """Create mock components for the example."""
    # Create mock heartbeat manager
    mock_heartbeat_manager = Mock()
    mock_heartbeat_manager.get_current_status.return_value = "running"
    mock_heartbeat_manager.get_last_applied.return_value = 0
    mock_heartbeat_manager.is_running.return_value = True
    mock_heartbeat_manager.update_last_applied = Mock()

    # Create mock operation handler
    mock_operation_handler = Mock()
    mock_operation_handler.heartbeat_manager = mock_heartbeat_manager
    mock_operation_handler.handle_operation.return_value = True
    mock_operation_handler.sync_operations_on_startup.return_value = True

    return mock_operation_handler, mock_heartbeat_manager


def test_agent_server():
    """Test the agent server functionality."""
    print("=== FastAPI Agent Server Example ===")
    
    setup_logging()
    
    # Create mock components
    operation_handler, heartbeat_manager = create_mock_components()
    
    # Create and start the server
    print("1. Creating FastAPI agent server...")
    server = AgentHttpServer(
        operation_handler=operation_handler,
        host='127.0.0.1',
        port=8080
    )
    
    print(f"   Server endpoint: {server.get_endpoint()}")
    print(f"   Server running: {server.is_running()}")
    
    # Start the server in a separate thread
    print("\n2. Starting server...")
    if server.start():
        print("   ✓ Server started successfully")
        
        # Give the server a moment to start
        time.sleep(2)
        
        # Test the endpoints
        test_endpoints(server.get_endpoint())
        
        # Stop the server
        print("\n6. Stopping server...")
        server.stop()
        print("   ✓ Server stopped")
        
    else:
        print("   ✗ Failed to start server")


def test_endpoints(endpoint: str):
    """Test the server endpoints."""
    base_url = f"http://{endpoint}"
    
    # Test health endpoint
    print("\n3. Testing health endpoint...")
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✓ Health check: {data['status']} - {data['message']}")
        else:
            print(f"   ✗ Health check failed: {response.status_code}")
    except Exception as e:
        print(f"   ✗ Health check error: {e}")

    # Test status endpoint
    print("\n4. Testing status endpoint...")
    try:
        response = requests.get(f"{base_url}/api/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✓ Status: {data['status']}, Last applied: {data['last_applied']}, Running: {data['running']}")
        else:
            print(f"   ✗ Status check failed: {response.status_code}")
    except Exception as e:
        print(f"   ✗ Status check error: {e}")

    # Test operation execution endpoint
    print("\n5. Testing operation execution...")
    operation_data = {
        "operation_type": "create",
        "object_type": "network",
        "redo_log_id": 123,
        "vlan_id": 1500,
        "data": {
            "tenant_id": "test-tenant",
            "subnet_data": {
                "ip_start": "*************0",
                "ip_end": "***************",
                "ip_gateway": "*************"
            }
        }
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/operation/execute",
            json=operation_data,
            timeout=5,
            headers={'Content-Type': 'application/json'}
        )
        if response.status_code == 200:
            data = response.json()
            print(f"   ✓ Operation executed: {data['message']}")
        else:
            print(f"   ✗ Operation execution failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"      Error: {error_data.get('detail', 'Unknown error')}")
            except:
                pass
    except Exception as e:
        print(f"   ✗ Operation execution error: {e}")

    # Test sync endpoint
    print("\n   Testing sync endpoint...")
    try:
        response = requests.post(f"{base_url}/api/sync", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✓ Sync completed: {data['message']}")
        else:
            print(f"   ✗ Sync failed: {response.status_code}")
    except Exception as e:
        print(f"   ✗ Sync error: {e}")


def demonstrate_real_usage():
    """Demonstrate how to use the server in a real scenario."""
    print("\n=== Real Usage Example ===")
    
    # This would be how you'd use it in a real agent
    print("In a real agent, you would:")
    print("1. Load agent configuration:")
    print("   config = AgentConfig.from_file('agent_config.yaml')")
    print()
    print("2. Create real components:")
    print("   heartbeat_manager = DatabaseHeartbeatManager(config, env_checker, api_server_url)")
    print("   operation_handler = OperationHandler(heartbeat_manager, command_processor)")
    print()
    print("3. Create and start the server:")
    print("   server = AgentHttpServer(operation_handler, host='0.0.0.0', port=8080)")
    print("   server.start()")
    print()
    print("4. The server will then:")
    print("   - Accept operations from core_service via HTTP POST")
    print("   - Execute operations using the operation_handler")
    print("   - Update last_applied via heartbeat_manager")
    print("   - Provide health and status endpoints")
    print()
    print("5. Example agent configuration:")
    print("   agent:")
    print("     node_id: node-1")
    print("     group_id: group-0")
    print("     endpoint: *************:8080")
    print("     heartbeat_interval: 30.0")


def compare_with_flask():
    """Compare the new FastAPI implementation with the old Flask approach."""
    print("\n=== FastAPI vs Flask Comparison ===")
    
    print("Advantages of FastAPI over Flask:")
    print("✓ Automatic API documentation (Swagger UI)")
    print("✓ Built-in request/response validation with Pydantic")
    print("✓ Better type hints and IDE support")
    print("✓ Async support out of the box")
    print("✓ Better performance")
    print("✓ Consistent with the main API server")
    print()
    print("Key differences in implementation:")
    print("- Request validation: Pydantic models vs manual validation")
    print("- Error handling: HTTPException vs Flask error responses")
    print("- Server: uvicorn vs Flask development server")
    print("- Documentation: Automatic OpenAPI spec generation")
    print()
    print("Access the API documentation at: http://127.0.0.1:8080/docs")


def main():
    """Main example function."""
    try:
        test_agent_server()
        demonstrate_real_usage()
        compare_with_flask()
        
        print("\n" + "=" * 50)
        print("FastAPI Agent Server Example Completed!")
        print("The agent HTTP server has been successfully migrated to FastAPI.")
        
        return 0
        
    except KeyboardInterrupt:
        print("\nExample interrupted by user")
        return 1
    except Exception as e:
        print(f"\nExample failed with error: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
