#!/usr/bin/env python3
"""
Example of using the migrated agent without etcd dependencies.
"""

from v_switch.config.agent_config import AgentConfig
from v_switch.agent.agent import VSwitchAgent
import sys
import os
import time
import logging
import signal
from unittest.mock import Mock

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))


def setup_logging():
    """Setup logging for the example."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def create_mock_config():
    """Create a mock configuration for testing."""
    config = Mock(spec=AgentConfig)
    config.agent = Mock()
    config.agent.node_id = "example-node-1"
    config.agent.group_id = "group-0"
    config.agent.endpoint = "192.168.1.100:8080"
    config.agent.heartbeat_interval = 30.0
    config.env_check = Mock()
    config.env_check.base = True
    config.env_check.ovs = True
    config.env_check.nftables = True

    return config


def demonstrate_agent_lifecycle():
    """Demonstrate the agent lifecycle."""
    print("=== Agent Migration Example ===")

    setup_logging()

    # Create mock configuration
    config = create_mock_config()

    print("1. Creating agent with new database-based architecture...")
    agent = VSwitchAgent(
        config=config,
        dry_run=True  # Use dry run for demo
    )

    print(f"   Agent node_id: {config.agent.node_id}")
    print(f"   Agent group_id: {config.agent.group_id}")
    print(f"   Agent endpoint: {config.agent.endpoint}")
    print(f"   Agent running: {agent.is_running()}")

    # Get initial status
    print("\n2. Getting initial agent status...")
    status = agent.get_status()
    for key, value in status.items():
        print(f"   {key}: {value}")

    print("\n3. Starting agent...")
    try:
        if agent.start():
            print("   ✓ Agent started successfully")
            print(f"   Agent running: {agent.is_running()}")

            # Get status after start
            print("\n4. Getting agent status after start...")
            status = agent.get_status()
            for key, value in status.items():
                print(f"   {key}: {value}")

            # Simulate running for a short time
            print("\n5. Agent running... (press Ctrl+C to stop)")
            try:
                while True:
                    time.sleep(5)
                    print(f"   Agent status: running={agent.is_running()}")
            except KeyboardInterrupt:
                print("\n   Received interrupt signal")

        else:
            print("   ✗ Failed to start agent")

    except Exception as e:
        print(f"   ✗ Error starting agent: {e}")

    finally:
        print("\n6. Stopping agent...")
        agent.stop()
        print(f"   Agent running: {agent.is_running()}")
        print("   ✓ Agent stopped")


def compare_old_vs_new():
    """Compare old etcd-based vs new database-based approach."""
    print("\n=== Old vs New Architecture Comparison ===")

    print("Old etcd-based architecture:")
    print("- ❌ Required etcd connection and watching")
    print("- ❌ Complex metadata and shard monitoring")
    print("- ❌ etcd-based task management")
    print("- ❌ etcd-based heartbeat registration")
    print("- ❌ Tight coupling with etcd infrastructure")
    print()

    print("New database-based architecture:")
    print("- ✅ HTTP-based operation reception")
    print("- ✅ Database-driven heartbeat via API calls")
    print("- ✅ Simplified startup and synchronization")
    print("- ✅ FastAPI HTTP server for operations")
    print("- ✅ Redo log-based operation synchronization")
    print("- ✅ Group-based agent management")
    print("- ✅ Decoupled from etcd infrastructure")
    print("- ✅ Environment check only on startup (performance optimized)")
    print()

    print("Key benefits of migration:")
    print("1. Simplified architecture - no complex etcd watching")
    print("2. Better scalability - MySQL can handle more data")
    print("3. Improved reliability - ACID transactions")
    print("4. Easier debugging - HTTP-based communication")
    print("5. Better monitoring - redo log audit trail")
    print("6. Consistent API - FastAPI throughout the system")
    print("7. Performance optimized - environment check only on startup")


def demonstrate_configuration():
    """Demonstrate the new configuration format."""
    print("\n=== Configuration Changes ===")

    print("Old agent configuration:")
    print("```yaml")
    print("agent:")
    print("  agent_id: agent-1")
    print("  heartbeat_interval: 30.0")
    print("```")
    print()

    print("New agent configuration:")
    print("```yaml")
    print("agent:")
    print("  node_id: node-1          # Changed from agent_id")
    print("  group_id: group-0        # New field for group assignment")
    print("  endpoint: 192.168.1.100:8080  # New field for HTTP server")
    print("  heartbeat_interval: 30.0")
    print("```")
    print()

    print("Configuration compatibility:")
    print("- agent_id is now mapped to node_id for backward compatibility")
    print("- group_id determines which operations the agent handles")
    print("- endpoint specifies where the agent HTTP server listens")
    print("- heartbeat_interval controls API heartbeat frequency")


def demonstrate_operation_flow():
    """Demonstrate the new operation flow."""
    print("\n=== New Operation Flow ===")

    print("1. Agent Startup (Optimized):")
    print("   a. Check environment (OVS, nftables, permissions) - STARTUP ONLY")
    print("   b. Start HTTP server on configured endpoint")
    print("   c. Register node in database via API heartbeat")
    print("   d. Sync missed operations from redo_log table")
    print("   e. Start periodic heartbeat to API server")
    print()

    print("2. Operation Reception:")
    print("   a. Core service writes operation to database")
    print("   b. Core service logs operation to redo_log table")
    print("   c. Core service calculates target group: vlan_id % total_groups")
    print("   d. Core service sends HTTP POST to agents in target group")
    print("   e. Agent receives operation via HTTP endpoint")
    print("   f. Agent executes operation and updates last_applied")
    print()

    print("3. Heartbeat Process (Optimized):")
    print("   a. Agent sends periodic HTTP POST to /api/heartbeat")
    print("   b. API server updates node_status table")
    print("   c. Heartbeat includes current last_applied value")
    print("   d. NO environment check during heartbeat (performance optimized)")
    print("   e. Failed heartbeats mark node as unhealthy")
    print()

    print("4. Synchronization:")
    print("   a. On startup, agent gets last_applied from database")
    print("   b. Agent queries redo_log for operations > last_applied")
    print("   c. Agent executes missed operations in order")
    print("   d. Agent updates last_applied after each operation")


def main():
    """Main example function."""
    try:
        demonstrate_agent_lifecycle()
        compare_old_vs_new()
        demonstrate_configuration()
        demonstrate_operation_flow()

        print("\n" + "=" * 60)
        print("Agent Migration Example Completed!")
        print("The agent has been successfully migrated from etcd to database.")
        print("\nNext steps:")
        print("1. Update agent configuration files with new format")
        print("2. Deploy agents with database connectivity")
        print("3. Test the full operation flow with real data")
        print("4. Monitor agent heartbeats in the database")

        return 0

    except KeyboardInterrupt:
        print("\nExample interrupted by user")
        return 1
    except Exception as e:
        print(f"\nExample failed with error: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
